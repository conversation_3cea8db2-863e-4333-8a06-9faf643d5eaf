-- 创建择校报告表
CREATE TABLE IF NOT EXISTS `ba_school_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `student_id` int(11) NOT NULL DEFAULT '0' COMMENT '学生ID',
  `target_major` varchar(255) NOT NULL DEFAULT '' COMMENT '目标专业',
  `target_major_code` varchar(50) NOT NULL DEFAULT '' COMMENT '目标专业代码',
  `dream_school` varchar(255) NOT NULL DEFAULT '' COMMENT '梦校',
  `dream_school_id` int(11) NOT NULL DEFAULT '0' COMMENT '梦校ID',
  `region_preference` varchar(255) NOT NULL DEFAULT '' COMMENT '地区倾向，JSON格式',
  `province_selection` text COMMENT '省份选择，JSON格式',
  `school_level` varchar(50) NOT NULL DEFAULT '' COMMENT '院校层次',
  `reference_books` text COMMENT '专业课制定参考书',
  `politics_score` int(11) NOT NULL DEFAULT '0' COMMENT '政治预估分数',
  `english_type` varchar(50) NOT NULL DEFAULT '' COMMENT '英语类型',
  `english_score` int(11) NOT NULL DEFAULT '0' COMMENT '英语预估分数',
  `math_type` varchar(50) NOT NULL DEFAULT '' COMMENT '数学类型',
  `math_score` int(11) NOT NULL DEFAULT '0' COMMENT '数学预估分数',
  `professional_score` int(11) NOT NULL DEFAULT '0' COMMENT '专业课预估分数',
  `total_score` int(11) NOT NULL DEFAULT '0' COMMENT '总分',
  `personal_needs` text COMMENT '个性化需求',
  `weak_modules` text COMMENT '薄弱模块',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='择校报告表';
