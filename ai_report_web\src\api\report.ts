import request from '@/utils/request'
import {
  ApiResponse,
  Report,
  ReportListParams,
  ReportListResult,
  GenerateReportParams,
  getMsgStrParams,
  getSchoolAndMajorParams,
  SearchParams,
  AiRecommendationParams,
  AiRecommendationResult,
  ReportNumResult,
  SignalSchoolDetailInfo 
} from '@/types'




export function ai_recommendation(data: AiRecommendationParams): Promise<ApiResponse<AiRecommendationResult>> {
  return request({
    url: '/remote/ai_recommendation',
    method: 'post',
    data
  })
}

/**
 * SSE流式AI推荐院校接口
 * @param data - AI推荐参数
 * @param onMessage - 接收消息的回调函数
 * @returns Promise<void>
 */
export function streamAiRecommendation(
  data: AiRecommendationParams,
  onMessage: (data: any) => void
): Promise<void> {
  return new Promise((resolve, reject) => {
    const token = localStorage.getItem('token');
    const url = `http://127.0.0.1:8787/api/remote/stream_ai_recommendation?report_id=${data.report_id}&token=${token}`;
    const eventSource = new EventSource(url);
    //let allData = "";
    // 常规消息处理
    eventSource.onmessage = (event) => {
      try {
        const data = event.data.replace("\n", "")
        console.log("event", event)
        onMessage({ type: event.type, data});
      } catch (error) {
        console.error('解析SSE数据失败:', error);
      }
    };
    
    // 处理开始事件
    eventSource.addEventListener('start', (event: MessageEvent) => {
      console.log('SSE流开始:', event.data);
      onMessage({ type: 'start', data: event.data });
    });
    
    // 处理结束事件
    eventSource.addEventListener('end', (event: MessageEvent) => {
      onMessage({ type: 'end', data: event.data });
      eventSource.close();
      resolve();
    });
    
    // 处理错误事件
    eventSource.addEventListener('error', (event: MessageEvent) => {
      console.error('SSE错误事件:', event);
      try {
        const data = JSON.parse(event.data);
        onMessage({ type: 'error', error: data.error || '未知错误' });
        eventSource.close();
        reject(new Error(data.error || '未知错误'));
      } catch (e) {
        onMessage({ type: 'error', error: '解析错误数据失败' });
        eventSource.close();
        reject(new Error('解析错误数据失败'));
      }
    });
    
    // 连接错误处理
    eventSource.onerror = (error) => {
    
      console.error('SSE连接错误:', error);
      eventSource.close();
      reject(error);
    };
  });
}

/**
 * 获取报告列表
 * @param params - 查询参数
 * @returns Promise
 */
export function getReportList(params: ReportListParams): Promise<ApiResponse<ReportListResult>> {
  return request({
    url: '/report/list',
    method: 'get',
    params
  })
}

/**
 * 获取报告详情
 * @param id - 报告ID
 * @returns Promise
 */
export function getReportDetail(id: number | string): Promise<ApiResponse<Report>> {
  return request({
    url: `/report/detail/${id}`,
    method: 'get'
  })
}

/**
 * 生成新报告
 * @param data - 报告数据
 * @returns Promise
 */
export function generateReport(data: GenerateReportParams): Promise<ApiResponse<Report>> {
  return request({
    url: '/report/generate',
    method: 'post',
    data
  })
}

/**
 * 删除报告
 * @param id - 报告ID
 * @returns Promise
 */
export function deleteReport(id: number | string): Promise<ApiResponse<null>> {
  return request({
    url: `/report/delete/${id}`,
    method: 'delete'
  })
}
/**
 * 获取推荐学校及分析prompt
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getReportBaseString(data: getMsgStrParams) : Promise<ApiResponse<Report>> {
  return request({
    url: '/remote/get_msg_str',
    method: 'post',
    data
  })
}

/**
 * 获取推荐学校及分析prompt
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getReportReasonString(data: getSchoolAndMajorParams) : Promise<ApiResponse<Report>>{
  return request({
    url: '/remote/school_and_major',
    method: 'post',
    data
  })
}


/**
 * 获取报告列表
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getExamYears() : Promise<ApiResponse<Report>>{
  return request({
    url: '/get_years',
    method: 'get',
  })
}

/**
 * 获取报告列表
 * @param {SearchParams} params - 查询参数
 * @returns {Promise} 返回报告列表
 */
export function reportList(params: SearchParams) : Promise<ApiResponse<Report>>{
  return request({
    url: '/report',
    method: 'get',
    params
  })
}

/**
 * 检查爬虫登录状态
 * @returns Promise
 */
export function checkSpiderLogin(): Promise<ApiResponse<any>> {
  return request({
    url: '/check_spider_login',
    method: 'get'
  })
}

/**
 * 获取AI推荐院校
 * @param {Object} data - 包含report_id的对象
 * @returns {Promise}
 */
export function getAiRecommendation(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/ai_recommendation',
    method: 'post',
    data
  })
}

/**
 * 保存报告信息
 * @param data - 报告保存数据
 * @returns Promise
 */
export function saveReport(data: {
  report_id: number | string;
  recommendations: Array<{
    report_id: number | string;
    school_id: number;
    competition_difficulty: string;
    suggestions: string;
  }>;
  pdf_url?: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/save_report',
    method: 'post',
    data
  })
}

export function getReportNum(): Promise<ApiResponse<ReportNumResult>> {
  return request({
    url: '/report_count',
    method: 'get'
  })
}

/**
 * 获取流式AI推荐院校 - 已弃用，使用普通API代替
 * @deprecated 不再使用流式API，请使用getAiRecommendation
 */
// 移除流式API相关代码

/**
 * 获取单个学校的详细信息
 * @param data - 包含学校名称和报告ID的参数
 * @returns Promise
 */
export function getSchoolDetail(data: { 
  school_name: string; 
  report_id: number | string; 
}): Promise<ApiResponse<SignalSchoolDetailInfo>> {
  return request({
    url: '/remote/get_school_detail',
    method: 'post',
    data
  })
}

export interface SaveReportDataParams {
  report_id: string | number;
  school_list: Array<{
    school_id: number;
    school_name: string;
    is_high_recommend: number; // 1 是高性价比推荐院校，0 不是
    difficulty_analysis: string;
    suggest: string;
    reason: string;
  }>;
}

/**
 * 保存报告数据到ba_report_info表
 */
export const saveReportData = (params: SaveReportDataParams) => {
  return request({
    url: '/remote/save_report_data',
    method: 'post',
    data: params
  })
}

// 添加更新PDF URL的API函数
export function updatePdfUrl(data: {
  report_id: number | string;
  pdf_url: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/report/pdf-url',
    method: 'put',
    data
  })
}

// 添加获取COS密钥的API函数
export function getCosKey(): Promise<ApiResponse<any>> {
  return request({
    url: '/coskey',
    method: 'get'
  })
}

export default {
  getReportList,
  getReportDetail,
  generateReport,
  deleteReport,
  getReportBaseString,
  getReportReasonString,
  reportList,
  getExamYears,
  checkSpiderLogin,
  getAiRecommendation,
  getReportNum,
  getSchoolDetail,
  saveReportData,
  updatePdfUrl,
  getCosKey,
}
