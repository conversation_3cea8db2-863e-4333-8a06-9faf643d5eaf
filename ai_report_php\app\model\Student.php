<?php
namespace app\model;

use think\Model;

class Student extends Model
{
    protected $table = 'ba_student';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 性别获取器
     * @param mixed $value
     * @return string
     */
    public function getSexTextAttr($value, $data)
    {
        $status = [
            1 => '男',
            2 => '女',
            3 => '其他'
        ];
        return $status[$data['sex']] ?? '未知';
    }

    /**
     * 是否研究生获取器
     * @param mixed $value
     * @return string
     */
    public function getIsPostgraduateTextAttr($value, $data)
    {
        return $data['is_postgraduate'] ? '是' : '否';
    }

    /**
     * 是否跨专业获取器
     * @param mixed $value
     * @return string
     */
    public function getIsCrossMajorTextAttr($value, $data)
    {
        return $data['is_cross_major'] ? '是' : '否';
    }

    /**
     * 标签关联
     * @return \think\model\relation\HasMany
     */
    public function tags()
    {
        return $this->hasMany(StudentTag::class, 'student_id', 'id');
    }

    /**
     * 详细信息关联
     * @return \think\model\relation\HasOne
     */
    public function detail()
    {
        return $this->hasOne(StudentDetail::class, 'student_id', 'id');
    }
}
