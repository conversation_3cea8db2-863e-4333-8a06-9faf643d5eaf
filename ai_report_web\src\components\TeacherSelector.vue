<template>
  <el-dialog v-model="dialogVisible" title="选择服务老师" width="800px" :close-on-click-modal="false" @closed="handleClosed">
    <div class="teacher-selector">
      <div class="search-container">
        <el-input v-model="keyword" placeholder="搜索老师昵称或手机号" clearable @input="handleSearch">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <el-table :data="teacherList" border style="width: 100%" v-loading="loading" highlight-current-row @row-click="handleSelectTeacher" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="50" align="center" />
        <el-table-column prop="nickname" label="老师昵称" align="center" />
        <el-table-column prop="username" label="用户名" align="center" />
        <el-table-column prop="mobile" label="手机号码" align="center" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button type="primary" size="small" @click.stop="handleSelectTeacher(scope.row)">
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" :current-page="currentPage" @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedTeacherId">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search, UserFilled } from "@element-plus/icons-vue";
import { getTeacherList, Teacher } from "@/api/user";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "select"]);

// 弹窗显示状态
const dialogVisible = ref(props.visible);

// 监听visible属性变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
  }
);

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (val) => {
  emit("update:visible", val);
  if (val) {
    // 弹窗打开时，重置数据并加载第一页
    resetData();
    fetchTeacherList();
  }
});

// 搜索关键字
const keyword = ref("");

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20); // 默认显示20条
const total = ref(0);

// 老师列表
const teacherList = ref<Teacher[]>([]);
const loading = ref(false);

// 选中的老师ID
const selectedTeacherId = ref<number | string>("");
const selectedTeacher = ref<Teacher | null>(null);

// 获取老师列表
const fetchTeacherList = async () => {
  loading.value = true;
  try {
    const res = await getTeacherList({
      page: currentPage.value,
      limit: pageSize.value,
      keyword: keyword.value,
    });

    if (res.code === 0) {
      teacherList.value = res.data;
      total.value = res.data.length || 0;
    } else {
      ElMessage.error(res.msg || "获取老师列表失败");
    }
  } catch (error) {
    console.error("获取老师列表失败", error);
    ElMessage.error("获取老师列表失败");
  } finally {
    loading.value = false;
  }
};

// 重置数据
const resetData = () => {
  keyword.value = "";
  currentPage.value = 1;
  selectedTeacherId.value = "";
  selectedTeacher.value = null;
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchTeacherList();
};

// 分页处理
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchTeacherList();
};

// 每页显示数量变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchTeacherList();
};

// 表格行样式
const tableRowClassName = ({ row }: { row: Teacher }) => {
  return selectedTeacherId.value === row.id ? "selected-row" : "";
};

// 选择老师
const handleSelectTeacher = (teacher: Teacher) => {
  selectedTeacherId.value = teacher.id;
  selectedTeacher.value = teacher;
};

// 确认选择
const handleConfirm = () => {
  if (selectedTeacher.value) {
    emit("select", selectedTeacher.value);
    dialogVisible.value = false;
  }
};

// 弹窗关闭后的处理
const handleClosed = () => {
  resetData();
};

// 初始化
onMounted(() => {
  if (dialogVisible.value) {
    fetchTeacherList();
  }
});
</script>

<style scoped>
.teacher-selector {
  padding: 0 10px;
}

.search-container {
  margin-bottom: 20px;
}

.teacher-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  min-height: 300px;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.teacher-item:hover {
  background-color: #f5f7fa;
}

.teacher-item.active {
  background-color: #e6f7f1;
}

.teacher-avatar {
  margin-right: 15px;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.teacher-username {
  font-size: 13px;
  color: #666;
}

.teacher-mobile {
  color: #1bb394;
  font-size: 14px;
}

.empty-tip {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #1bb394;
}

:deep(.el-table .selected-row) {
  background-color: #e6f7f1;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-button--primary) {
  background-color: #1bb394;
  border-color: #1bb394;
}

:deep(.el-button--primary:hover) {
  background-color: #19a588;
  border-color: #19a588;
}
</style>
