import request from '@/utils/request'
import { ApiResponse } from '@/types'

/**
 * 获取学生列表
 * @param params - 查询参数
 * @returns Promise
 */
export function getStudentList(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/student/list',
    method: 'get',
    params
  })
}

/**
 * 获取学生详情
 * @param id - 学生ID
 * @returns Promise
 */
export function getStudentDetail(id: number | string): Promise<ApiResponse<any>> {
  return request({
    url: '/student/detail',
    method: 'get',
    params: { id }
  })
}

/**
 * 添加学生
 * @param data - 学生数据
 * @returns Promise
 */
export function addStudent(data: any): Promise<ApiResponse<any>> {
  return request({
    url: '/student/add',
    method: 'post',
    data
  })
}

/**
 * 编辑学生
 * @param data - 学生数据
 * @returns Promise
 */
export function editStudent(data: any): Promise<ApiResponse<any>> {
  return request({
    url: '/student/edit',
    method: 'post',
    data
  })
}

/**
 * 删除学生
 * @param id - 学生ID
 * @returns Promise
 */
export function deleteStudent(id: number | string): Promise<ApiResponse<any>> {
  return request({
    url: '/student/delete',
    method: 'get',
    params: { id }
  })
}

/**
 * 更新学生的服务老师
 * @param data - 包含学生ID数组和老师ID
 * @returns Promise
 */
export function updateTeacher(data: { studentIds: (number | string)[]; teacherId: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/student/update-teacher',
    method: 'post',
    data
  })
}

/**
 * 处理 toggleAIOverlay 请求
 * 根据手机号查询学生，存在则更新，不存在则添加
 * @param data - 学生数据
 * @returns Promise
 */
export function toggleAIOverlay(data: any): Promise<ApiResponse<any>> {
  return request({
    url: '/student/toggle-ai-overlay',
    method: 'post',
    data
  })
}

export default {
  getStudentList,
  getStudentDetail,
  addStudent,
  editStudent,
  deleteStudent,
  updateTeacher,
  toggleAIOverlay
}
