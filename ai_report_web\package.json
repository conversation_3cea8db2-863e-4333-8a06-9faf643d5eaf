{"name": "ai_report_web", "private": true, "version": "0.0.1", "type": "module", "description": "基于Vue3+Element Plus的后台管理系统", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"ai_report_web": "file:", "axios": "^1.8.4", "cos-js-sdk-v5": "^1.10.1", "echarts": "^5.6.0", "element-plus": "^2.9.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "pinia": "^3.0.2", "three": "^0.176.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/html2canvas": "^1.0.0", "@types/node": "^20.17.45", "@vitejs/plugin-vue": "^5.2.2", "less": "^4.3.0", "typescript": "^5.4.5", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.1", "vue-tsc": "^2.0.7"}}