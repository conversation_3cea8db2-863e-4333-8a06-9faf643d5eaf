<?php

namespace app\controller;

use support\Request;
use Workerman\Coroutine;
use Workerman\Timer;
class IndexController
{
    protected $noNeedLogin = ['*']; // 不需要登录的方法
    public function index(Request $request)
    {
        static $readme;
        if (!$readme) {
            $readme = file_get_contents(base_path('README.md'));
        }
        return $readme;
    }

    public function view(Request $request)
    {
        return view('index/view', ['name' => 'webman']);
    }

    public function json(Request $request)
    {
        return json([
            "code"=>1
        ]);
    }
}
