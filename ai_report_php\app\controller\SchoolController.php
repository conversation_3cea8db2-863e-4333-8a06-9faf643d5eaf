<?php
namespace app\controller;

use app\model\School;
use app\model\SchoolMajor;
use app\model\Major;
use app\model\SchoolInfo;
use app\model\NationalLine;
use support\Request;
use support\Log;
use support\Db;

class SchoolController
{
    /**
     * 搜索学校
     * @param Request $request
     * @return \support\Response
     */
    public function search(Request $request)
    {
        $keyword = $request->get('keyword', '');
        if (empty($keyword)) {
            return json([
                'code' => 400,
                'msg' => '请输入搜索关键词',
                'data' => []
            ]);
        }

        $schools = School::where('name', 'like', "%{$keyword}%")
            ->field(['id', 'name'])
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $schools
        ]);
    }

    /**
     * 搜索专业
     * @param Request $request
     * @return \support\Response
     */
    public function searchSchoolMajor(Request $request)
    {
        $schoolId = $request->get('school_id', 0);
        $majorName = $request->get('major_name', '');

        // 验证学校ID
        if (empty($schoolId)) {
            return json([
                'code' => 400,
                'msg' => '请选择学校',
                'data' => []
            ]);
        }

        // 构建查询条件
        $query = SchoolMajor::where('school_id', $schoolId)
            ->where('is_delete', 0)
            ->where('type', 0); // 只查询专业，不查询门类

        // 如果有专业名称，添加模糊搜索条件
        if (!empty($majorName)) {
            $query = $query->where('major_name', 'like', "%{$majorName}%");
        }

        // 获取数据
        $majors = $query->field([
            'id',
            'major_name',
        ])->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $majors
        ]);
    }

    /**
     * 搜索二级学科
     * @param Request $request
     * @return \support\Response
     */
    public function searchMajor(Request $request)
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword)) {
            return json([
                'code' => 400,
                'msg' => '请输入搜索关键词',
                'data' => []
            ]);
        }

        $result = Major::where('erji_name', 'like', "%{$keyword}%")
            ->field([
                'erji_name as name',
                'major_code as code'
            ])
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取考研年份列表
     * @return \support\Response
     */
    public function getExamYears()
    {
        // 从配置文件中获取考研年份列表
        $years = config('exam_years.years', []);

        // 转换为前端需要的格式
        $result = [];
        foreach ($years as $key => $value) {
            $result[] = [
                'value' => $key,
                'label' => $value
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 根据预估分数和专业信息获取院校数据
     * 该接口不需要鉴权
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolsByScore(Request $request)
    {
        // 记录请求参数
        $requestParams = $request->all();
        Log::info('获取院校数据请求参数: ' . json_encode($requestParams, JSON_UNESCAPED_UNICODE));

        // 获取请求参数
        $score = (int)$request->get('score', 0); // 预估分数
        $majorCode = $request->get('major_code', ''); // 专业代码
        $majorName = $request->get('major_name', ''); // 专业名称
        $provinces = $request->get('provinces', ''); // 省份，多个省份用逗号分隔
        $limit = (int)$request->get('limit', 20); // 返回数量限制，默认20条

        // 验证参数
        if ($score <= 0) {
            return json([
                'code' => 400,
                'msg' => '预估分数不能为空',
                'data' => []
            ]);
        }

        // 构建查询条件
        $query = SchoolInfo::where('must_reach_score', '<=', $score);

        // 如果有专业代码，添加专业代码条件
        if (!empty($majorCode)) {
            $query = $query->where('second_level_name', $majorCode);
        }

        // 如果有专业名称，添加专业名称条件
        if (!empty($majorName)) {
            $query = $query->where('major_name', 'like', "%{$majorName}%");
        }

        // 如果有省份，添加省份条件
        if (!empty($provinces)) {
            $provinceArray = explode(',', $provinces);
            if (!empty($provinceArray)) {
                $query = $query->whereIn('province', $provinceArray);
            }
        }

        // 按必达分排序（从高到低）
        $query = $query->order('must_reach_score', 'desc');

        // 获取数据
        $schools = $query->limit($limit)->select();

        // 处理返回数据
        $result = [];
        foreach ($schools as $school) {
            // 计算分差
            $scoreDiff = $score - $school['must_reach_score'];

            // 构建返回数据
            $result[] = [
                'id' => $school['id'],
                'school_name' => $school['school_name'],
                'province' => $school['province'],
                'area' => $school['area'],
                'college' => $school['college'],
                'major_name' => $school['major_name'],
                'first_level_code' => $school['first_level_code'],
                'xueke_name' => $school['xueke_name'],
                'second_level_name' => $school['second_level_name'],
                'school_type' => $school['school_type'],
                'study_type' => $school['study_type'],
                'warning_level' => $school['warning_level'],
                'must_reach_score' => $school['must_reach_score'],
                'score_diff' => $scoreDiff,
                'ranking' => $school['ranking'],
                'admission' => $school['admission'],
                'course_suggestion' => $school['course_suggestion'],
                'detail_url' => $school['detail_url'],
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取国家线数据
     * 根据专业代码获取对应一级学科的最近三年国家线数据
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getNationalLineData(Request $request)
    {
        // 获取请求参数
        $majorCode = $request->get('major_code', '');

        // 验证参数
        if (empty($majorCode)) {
            return json([
                'code' => 400,
                'msg' => '专业代码不能为空',
                'data' => []
            ]);
        }

        // 记录请求参数
        Log::info('获取国家线数据请求参数: ' . json_encode(['major_code' => $majorCode], JSON_UNESCAPED_UNICODE));

        try {
            // 使用模型获取国家线数据
            $result = NationalLine::getNationalLineByMajorCode($majorCode);

            if (empty($result)) {
                return json([
                    'code' => 400,
                    'msg' => '未找到对应的国家线数据',
                    'data' => []
                ]);
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('获取国家线数据异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取国家线数据失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}