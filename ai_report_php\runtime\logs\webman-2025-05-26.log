[2025-05-26 08:42:52] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 08:43:02] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 08:43:03] default.INFO: 四级成绩: 460 [] []
[2025-05-26 08:43:03] default.INFO: 六级成绩: 500 [] []
[2025-05-26 08:43:03] default.INFO: 托福成绩:  [] []
[2025-05-26 08:43:03] default.INFO: 英语能力: 一般 [] []
[2025-05-26 08:43:03] default.INFO: 地区倾向: A区 [] []
[2025-05-26 08:43:04] default.INFO: AI推荐学校请求参数: {"report_id":108} [] []
[2025-05-26 08:43:05] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 08:43:05] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 08:43:05] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 08:43:05] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 08:43:05] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 08:43:05] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 08:43:06] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 08:43:07] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 08:43:09] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 08:43:09] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 08:43:10] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 08:43:11] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 08:43:13] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 08:43:13] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 08:43:16] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 08:43:17] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 08:43:17] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 08:43:19] default.INFO: 开始保存推荐院校数据到数据库，reportId: 108, studentId: 1 [] []
[2025-05-26 08:43:21] default.INFO: 已删除报告ID 108 的旧推荐数据 [] []
[2025-05-26 08:43:21] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 08:43:22] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 08:43:22] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 08:43:22] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 08:43:23] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:01:19] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 09:01:30] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 09:01:30] default.INFO: 四级成绩: 460 [] []
[2025-05-26 09:01:30] default.INFO: 六级成绩: 500 [] []
[2025-05-26 09:01:30] default.INFO: 托福成绩:  [] []
[2025-05-26 09:01:30] default.INFO: 英语能力: 一般 [] []
[2025-05-26 09:01:30] default.INFO: 地区倾向: A区 [] []
[2025-05-26 09:01:31] default.INFO: AI推荐学校请求参数: {"report_id":109} [] []
[2025-05-26 09:01:31] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 09:01:32] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 09:01:32] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 09:01:32] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 09:01:32] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 09:01:32] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 09:01:32] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 09:01:33] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 09:01:33] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:01:34] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 09:01:34] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 09:01:35] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 09:01:35] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 09:01:36] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 09:01:36] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 09:01:37] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 09:01:37] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:01:38] default.INFO: 开始保存推荐院校数据到数据库，reportId: 109, studentId: 1 [] []
[2025-05-26 09:01:38] default.INFO: 已删除报告ID 109 的旧推荐数据 [] []
[2025-05-26 09:01:38] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 09:01:38] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 09:01:38] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 09:01:38] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 09:01:39] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:02:37] default.INFO: 保存报告信息 {"report_id":109,"recommendations_count":10,"pdf_url":"zxw-1300870289.cos.ap-nanjing.myqcloud.com/file/20250526/20250526_889244.pdf"} []
[2025-05-26 09:05:10] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 09:05:10] default.INFO: 四级成绩: 460 [] []
[2025-05-26 09:05:10] default.INFO: 六级成绩: 500 [] []
[2025-05-26 09:05:10] default.INFO: 托福成绩:  [] []
[2025-05-26 09:05:10] default.INFO: 英语能力: 一般 [] []
[2025-05-26 09:05:10] default.INFO: 地区倾向: A区 [] []
[2025-05-26 09:05:11] default.INFO: AI推荐学校请求参数: {"report_id":110} [] []
[2025-05-26 09:05:12] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 09:05:12] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 09:05:12] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 09:05:12] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 09:05:12] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 09:05:12] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 09:05:12] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 09:05:12] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 09:05:13] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:05:13] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 09:05:13] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 09:05:14] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 09:05:14] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 09:05:14] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 09:05:15] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 09:05:15] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 09:05:15] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:05:15] default.INFO: 开始保存推荐院校数据到数据库，reportId: 110, studentId: 1 [] []
[2025-05-26 09:05:16] default.INFO: 已删除报告ID 110 的旧推荐数据 [] []
[2025-05-26 09:05:16] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 09:05:16] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 09:05:16] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 09:05:16] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 09:05:22] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:07:34] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 09:07:34] default.INFO: 四级成绩: 460 [] []
[2025-05-26 09:07:34] default.INFO: 六级成绩: 500 [] []
[2025-05-26 09:07:34] default.INFO: 托福成绩:  [] []
[2025-05-26 09:07:34] default.INFO: 英语能力: 一般 [] []
[2025-05-26 09:07:34] default.INFO: 地区倾向: A区 [] []
[2025-05-26 09:07:35] default.INFO: AI推荐学校请求参数: {"report_id":111} [] []
[2025-05-26 09:07:35] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 09:07:36] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 09:07:36] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 09:07:36] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 09:07:36] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 09:07:36] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 09:07:36] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 09:07:36] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 09:07:37] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:07:37] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 09:07:37] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 09:07:38] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 09:07:38] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 09:07:38] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 09:07:39] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 09:07:39] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 09:07:40] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:07:40] default.INFO: 开始保存推荐院校数据到数据库，reportId: 111, studentId: 1 [] []
[2025-05-26 09:07:40] default.INFO: 已删除报告ID 111 的旧推荐数据 [] []
[2025-05-26 09:07:40] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 09:07:40] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 09:07:40] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 09:07:40] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 09:07:41] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:13:12] default.INFO: 保存报告信息 {"report_id":111,"recommendations_count":10,"pdf_url":"zxw-1300870289.cos.ap-nanjing.myqcloud.com/file/20250526/20250526_728186.pdf"} []
[2025-05-26 09:13:18] default.INFO: 保存报告信息 {"report_id":111,"recommendations_count":10,"pdf_url":"zxw-1300870289.cos.ap-nanjing.myqcloud.com/file/20250526/20250526_065804.pdf"} []
[2025-05-26 09:18:46] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 09:18:46] default.INFO: 四级成绩: 460 [] []
[2025-05-26 09:18:46] default.INFO: 六级成绩: 500 [] []
[2025-05-26 09:18:46] default.INFO: 托福成绩:  [] []
[2025-05-26 09:18:46] default.INFO: 英语能力: 一般 [] []
[2025-05-26 09:18:46] default.INFO: 地区倾向: A区 [] []
[2025-05-26 09:18:47] default.INFO: AI推荐学校请求参数: {"report_id":112} [] []
[2025-05-26 09:18:47] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 09:18:47] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 09:18:47] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 09:18:47] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 09:18:47] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 09:18:47] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 09:18:48] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 09:18:48] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 09:18:49] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:18:49] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 09:18:49] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 09:18:50] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 09:18:50] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 09:18:50] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 09:18:51] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 09:18:51] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 09:18:51] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:18:51] default.INFO: 开始保存推荐院校数据到数据库，reportId: 112, studentId: 1 [] []
[2025-05-26 09:18:51] default.INFO: 已删除报告ID 112 的旧推荐数据 [] []
[2025-05-26 09:18:52] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 09:18:52] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 09:18:52] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 09:18:52] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 09:18:53] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:24:09] default.INFO: 保存报告信息 {"report_id":112,"recommendations_count":10,"pdf_url":""} []
[2025-05-26 09:27:53] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 09:27:53] default.INFO: 四级成绩: 460 [] []
[2025-05-26 09:27:53] default.INFO: 六级成绩: 500 [] []
[2025-05-26 09:27:53] default.INFO: 托福成绩:  [] []
[2025-05-26 09:27:53] default.INFO: 英语能力: 一般 [] []
[2025-05-26 09:27:53] default.INFO: 地区倾向: A区 [] []
[2025-05-26 09:27:54] default.INFO: AI推荐学校请求参数: {"report_id":113} [] []
[2025-05-26 09:27:54] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 09:27:54] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 09:27:54] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 09:27:54] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 09:27:54] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 09:27:54] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 09:27:55] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 09:27:55] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 09:27:56] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:27:56] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 09:27:56] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 09:27:57] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 09:27:57] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 09:27:57] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 09:27:58] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 09:27:58] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 09:27:59] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 09:27:59] default.INFO: 开始保存推荐院校数据到数据库，reportId: 113, studentId: 1 [] []
[2025-05-26 09:27:59] default.INFO: 已删除报告ID 113 的旧推荐数据 [] []
[2025-05-26 09:27:59] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 09:28:00] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 09:28:00] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 09:28:00] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 09:28:01] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 09:28:04] default.ERROR: 获取国家线数据异常: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms [] []
[2025-05-26 10:52:40] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 10:52:46] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 10:52:47] default.INFO: 四级成绩: 460 [] []
[2025-05-26 10:52:47] default.INFO: 六级成绩: 500 [] []
[2025-05-26 10:52:47] default.INFO: 托福成绩:  [] []
[2025-05-26 10:52:47] default.INFO: 英语能力: 一般 [] []
[2025-05-26 10:52:47] default.INFO: 地区倾向: A区 [] []
[2025-05-26 10:52:47] default.INFO: AI推荐学校请求参数: {"report_id":114} [] []
[2025-05-26 10:52:48] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 10:52:48] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 10:52:48] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 10:52:48] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 10:52:48] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 10:52:48] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 10:52:48] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 10:52:49] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 10:52:49] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 10:52:50] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 10:52:50] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 10:52:51] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 10:52:52] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 10:52:55] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 10:52:55] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 10:52:56] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 10:52:57] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 10:52:57] default.INFO: 开始保存推荐院校数据到数据库，reportId: 114, studentId: 1 [] []
[2025-05-26 10:52:57] default.INFO: 已删除报告ID 114 的旧推荐数据 [] []
[2025-05-26 10:52:57] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 10:52:58] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 10:52:58] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 10:52:58] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 10:52:58] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 10:54:39] default.INFO: 保存报告信息 {"report_id":114,"recommendations_count":10,"pdf_url":"zxw-1300870289.cos.ap-nanjing.myqcloud.com/file/20250526/20250526_230244.pdf"} []
[2025-05-26 11:08:05] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 11:08:05] default.INFO: 四级成绩: 460 [] []
[2025-05-26 11:08:05] default.INFO: 六级成绩: 500 [] []
[2025-05-26 11:08:05] default.INFO: 托福成绩:  [] []
[2025-05-26 11:08:05] default.INFO: 英语能力: 一般 [] []
[2025-05-26 11:08:05] default.INFO: 地区倾向: A区 [] []
[2025-05-26 11:08:06] default.INFO: AI推荐学校请求参数: {"report_id":115} [] []
[2025-05-26 11:08:06] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 11:08:06] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 11:08:06] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 11:08:06] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 11:08:06] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 11:08:06] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 11:08:06] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 11:08:07] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 11:08:07] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:08:07] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 11:08:08] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 11:08:08] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 11:08:08] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 11:08:09] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 11:08:09] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 11:08:10] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 11:08:10] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:08:10] default.INFO: 开始保存推荐院校数据到数据库，reportId: 115, studentId: 1 [] []
[2025-05-26 11:08:10] default.INFO: 已删除报告ID 115 的旧推荐数据 [] []
[2025-05-26 11:08:10] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 11:08:11] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 11:08:11] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 11:08:11] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 11:08:12] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 11:47:05] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 11:47:33] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 11:47:33] default.INFO: 四级成绩: 460 [] []
[2025-05-26 11:47:33] default.INFO: 六级成绩: 500 [] []
[2025-05-26 11:47:33] default.INFO: 托福成绩:  [] []
[2025-05-26 11:47:33] default.INFO: 英语能力: 一般 [] []
[2025-05-26 11:47:33] default.INFO: 地区倾向: A区 [] []
[2025-05-26 11:47:34] default.INFO: AI推荐学校请求参数: {"report_id":116} [] []
[2025-05-26 11:47:34] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 11:47:34] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 11:47:34] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 11:47:34] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 11:47:34] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 11:47:34] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 11:47:35] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 11:47:35] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 11:47:35] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:47:36] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 11:47:36] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 11:47:37] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 11:47:37] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 11:47:37] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 11:47:38] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 11:47:38] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 11:47:38] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:47:39] default.INFO: 开始保存推荐院校数据到数据库，reportId: 116, studentId: 1 [] []
[2025-05-26 11:47:39] default.INFO: 已删除报告ID 116 的旧推荐数据 [] []
[2025-05-26 11:47:39] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 11:47:39] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 11:47:39] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 11:47:39] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 11:47:40] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 11:51:47] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 11:51:48] default.INFO: 四级成绩: 460 [] []
[2025-05-26 11:51:48] default.INFO: 六级成绩: 500 [] []
[2025-05-26 11:51:48] default.INFO: 托福成绩:  [] []
[2025-05-26 11:51:48] default.INFO: 英语能力: 一般 [] []
[2025-05-26 11:51:48] default.INFO: 地区倾向: A区 [] []
[2025-05-26 11:51:48] default.INFO: AI推荐学校请求参数: {"report_id":117} [] []
[2025-05-26 11:51:49] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 11:51:49] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 11:51:49] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 11:51:49] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 11:51:49] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 11:51:49] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 11:51:49] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 11:51:49] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 11:51:50] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:51:50] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 11:51:51] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 11:51:51] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 11:51:51] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 11:51:52] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 11:51:52] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 11:51:52] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 11:51:53] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:51:53] default.INFO: 开始保存推荐院校数据到数据库，reportId: 117, studentId: 1 [] []
[2025-05-26 11:51:53] default.INFO: 已删除报告ID 117 的旧推荐数据 [] []
[2025-05-26 11:51:53] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 11:51:54] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 11:51:54] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 11:51:54] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 11:51:54] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 11:56:10] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 11:56:10] default.INFO: 四级成绩: 460 [] []
[2025-05-26 11:56:10] default.INFO: 六级成绩: 500 [] []
[2025-05-26 11:56:10] default.INFO: 托福成绩:  [] []
[2025-05-26 11:56:10] default.INFO: 英语能力: 一般 [] []
[2025-05-26 11:56:10] default.INFO: 地区倾向: A区 [] []
[2025-05-26 11:56:11] default.INFO: AI推荐学校请求参数: {"report_id":118} [] []
[2025-05-26 11:56:11] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 11:56:11] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 11:56:11] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 11:56:11] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 11:56:11] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 11:56:11] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 11:56:12] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 11:56:12] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 11:56:12] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:56:13] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 11:56:13] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 11:56:14] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 11:56:14] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 11:56:14] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 11:56:15] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 11:56:15] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 11:56:15] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:56:15] default.INFO: 开始保存推荐院校数据到数据库，reportId: 118, studentId: 1 [] []
[2025-05-26 11:56:16] default.INFO: 已删除报告ID 118 的旧推荐数据 [] []
[2025-05-26 11:56:16] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 11:56:16] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 11:56:16] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 11:56:16] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 11:56:17] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 11:58:02] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 11:58:02] default.INFO: 四级成绩: 460 [] []
[2025-05-26 11:58:02] default.INFO: 六级成绩: 500 [] []
[2025-05-26 11:58:02] default.INFO: 托福成绩:  [] []
[2025-05-26 11:58:02] default.INFO: 英语能力: 一般 [] []
[2025-05-26 11:58:02] default.INFO: 地区倾向: A区 [] []
[2025-05-26 11:58:03] default.INFO: AI推荐学校请求参数: {"report_id":119} [] []
[2025-05-26 11:58:03] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 11:58:03] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 11:58:03] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 11:58:03] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 11:58:03] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 11:58:03] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 11:58:04] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 11:58:04] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 11:58:04] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:58:05] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 11:58:05] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 11:58:05] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 11:58:06] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 11:58:06] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 11:58:06] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 11:58:07] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 11:58:07] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 11:58:07] default.INFO: 开始保存推荐院校数据到数据库，reportId: 119, studentId: 1 [] []
[2025-05-26 11:58:07] default.INFO: 已删除报告ID 119 的旧推荐数据 [] []
[2025-05-26 11:58:07] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 11:58:08] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 11:58:08] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 11:58:08] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 11:58:08] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 11:59:01] default.INFO: 保存报告信息 {"report_id":119,"recommendations_count":10,"pdf_url":"zxw-1300870289.cos.ap-nanjing.myqcloud.com/file/20250526/20250526_680035.pdf"} []
[2025-05-26 13:32:15] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 13:32:22] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 13:32:22] default.INFO: 四级成绩: 460 [] []
[2025-05-26 13:32:22] default.INFO: 六级成绩: 500 [] []
[2025-05-26 13:32:22] default.INFO: 托福成绩:  [] []
[2025-05-26 13:32:22] default.INFO: 英语能力: 一般 [] []
[2025-05-26 13:32:22] default.INFO: 地区倾向: A区 [] []
[2025-05-26 13:32:23] default.INFO: AI推荐学校请求参数: {"report_id":120} [] []
[2025-05-26 13:32:23] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 13:32:23] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 13:32:23] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 13:32:23] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 13:32:23] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 13:32:23] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 13:32:24] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 13:32:24] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 13:32:24] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 13:32:25] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 13:32:25] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 13:32:25] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 13:32:26] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 13:32:26] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 13:32:26] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 13:32:27] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 13:32:27] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 13:32:27] default.INFO: 开始保存推荐院校数据到数据库，reportId: 120, studentId: 1 [] []
[2025-05-26 13:32:27] default.INFO: 已删除报告ID 120 的旧推荐数据 [] []
[2025-05-26 13:32:27] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 13:32:28] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 13:32:28] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 13:32:28] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","北京科技大学","中国科学技术大学"] [] []
[2025-05-26 13:32:28] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 13:49:14] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 13:51:04] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 13:51:04] default.INFO: 四级成绩: 460 [] []
[2025-05-26 13:51:04] default.INFO: 六级成绩: 500 [] []
[2025-05-26 13:51:04] default.INFO: 托福成绩:  [] []
[2025-05-26 13:51:04] default.INFO: 英语能力: 一般 [] []
[2025-05-26 13:51:04] default.INFO: 地区倾向: A区 [] []
[2025-05-26 13:51:05] default.INFO: AI推荐学校请求参数: {"report_id":121} [] []
[2025-05-26 13:51:05] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 13:51:06] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 13:51:06] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 13:51:06] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 13:51:06] default.INFO: 上下文长度: 3733 字符 [] []
[2025-05-26 13:51:06] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 13:51:06] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张三\n性别：男\n本科院校：安徽大学\n本科专业：计算机科学与技术\n目标专业：(081200)计算机科学与技术（专业代码：081200）\n是否跨专业：否\n\n考试成绩预估：\n政治：70分\n英语（英语一）：80分\n数学（数学一）：90分\n专业课：110分\n总分：350分\n\n目标偏好：\n目标区域：A区\n目标省份：安徽省,上海市,北京市,江苏省,浙江省\n院校层次：211\n梦想院校：中国科学技术大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081200\n- 预估总分：350分\n- 目标区域：A区\n- 目标省份：安徽省,上海市,北京市,江苏省,浙江省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 北京科技大学（北京市）- 081200计算机科学与技术，分数线：369分，低于分数线19分\n2. 南京理工大学（江苏省）- 081200计算机科学与技术，分数线：344分，高出分数线6分\n3. 北京邮电大学（北京市）- 081200计算机科学与技术，分数线：340分，高出分数线10分\n4. 合肥工业大学（安徽省）- 081200计算机科学与技术，分数线：333分，高出分数线17分\n5. 苏州大学（江苏省）- 081200计算机科学与技术，分数线：330分，高出分数线20分\n6. 江南大学（江苏省）- 081200计算机科学与技术，分数线：326分，高出分数线24分\n7. 安徽大学（安徽省）- 081200计算机科学与技术，分数线：324分，高出分数线26分\n8. 河海大学（江苏省）- 081200计算机科学与技术，分数线：320分，高出分数线30分\n9. 东华大学（上海市）- 081200计算机科学与技术，分数线：318分，高出分数线32分\n\n【985,211,双一流类院校】\n1. 中国科学技术大学（安徽省）- 081200计算机科学与技术，分数线：363分，低于分数线13分\n\n【按省份分类】\n北京市：北京科技大学、北京邮电大学\n江苏省：南京理工大学、苏州大学、江南大学、河海大学\n安徽省：合肥工业大学、安徽大学、中国科学技术大学\n上海市：东华大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 13:52:08] default.INFO: DeepSeek API 请求总时间: 62.536132097244秒 [] []
[2025-05-26 13:52:08] default.INFO: DeepSeek API 响应大小: 7205字节 [] []
[2025-05-26 13:52:08] default.INFO: DeepSeek API 响应: 
{"id":"d4096f9f-78c4-437c-8321-2fb10d6b87d2","object":"chat.completion","created":1748238665,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"南京理工大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，略高于学生预估总分350分。该校在江苏省内声誉良好，计算机专业实力较强，报考人数较多，但录取比例相对合理。\",\n            \"suggest\": \"建议加强专业课复习，尤其是数据结构与算法，同时提升数学成绩至100分以上以增加竞争力。\",\n            \"reason\": \"南京理工大学计算机专业实力雄厚，地理位置优越，就业前景好。学生预估分数接近分数线，有较大录取机会。\"\n        },\n        {\n            \"school_name\": \"北京邮电大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"北京邮电大学计算机专业竞争非常激烈，分数线为340分，但实际录取分数通常更高。该校计算机专业在全国排名靠前，报考人数众多，竞争压力大。\",\n            \"suggest\": \"建议将总分目标提升至360分以上，重点突破专业课和数学，同时提前联系导师。\",\n            \"reason\": \"北邮计算机专业声誉极高，毕业生就业前景极佳。学生预估分数略高于分数线，但需进一步提升以应对激烈竞争。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"合肥工业大学计算机专业竞争适中，分数线为333分，学生预估分数高出17分。该校在安徽省内认可度高，计算机专业实力较强，竞争相对温和。\",\n            \"suggest\": \"保持当前复习节奏，重点巩固专业课知识，尤其是操作系统和计算机网络。\",\n            \"reason\": \"合工大计算机专业性价比高，学生预估分数优势明显，录取概率大，且离家近方便就读。\"\n        },\n        {\n            \"school_name\": \"苏州大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"苏州大学计算机专业竞争较为温和，分数线为330分，学生预估分数高出20分。该校计算机专业发展迅速，但报考热度相对低于一线城市高校。\",\n            \"suggest\": \"维持现有分数水平，重点关注专业课历年真题，提升编程实践能力。\",\n            \"reason\": \"苏州大学计算机专业实力不断提升，地理位置优越，学生分数优势明显，录取把握大。\"\n        },\n        {\n            \"school_name\": \"江南大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"江南大学计算机专业竞争相对较小，分数线为326分，学生预估分数高出24分。该校计算机专业并非王牌专业，报考热度适中。\",\n            \"suggest\": \"可适当降低复习强度，但仍需保持专业课和数学的稳定发挥。\",\n            \"reason\": \"江南大学录取门槛相对较低，学生分数优势显著，适合求稳选择，且无锡地理位置优越。\"\n        },\n        {\n            \"school_name\": \"安徽大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"安徽大学计算机专业竞争较小，分数线为324分，学生预估分数高出26分。作为母校，录取时可能有一定优势。\",\n            \"suggest\": \"可重点准备复试内容，尤其是项目经验和专业英语。\",\n            \"reason\": \"安大计算机专业录取概率极高，母校背景可能带来额外优势，且生活成本低。\"\n        },\n        {\n            \"school_name\": \"河海大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"河海大学计算机专业竞争温和，分数线为320分，学生预估分数高出30分。该校以水利为特色，计算机专业报考热度较低。\",\n            \"suggest\": \"保持现有复习计划，可适当增加编程实践时间。\",\n            \"reason\": \"河海大学计算机专业录取把握极大，南京地理位置优越，适合追求稳妥录取的学生。\"\n        },\n        {\n            \"school_name\": \"东华大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"东华大学计算机专业竞争较小，分数线为318分，学生预估分数高出32分。该校以纺织为特色，计算机专业报考人数有限。\",\n            \"suggest\": \"重点准备复试环节，尤其是跨学科知识的应用。\",\n            \"reason\": \"东华大学录取门槛低，上海地理位置优越，学生分数优势明显，适合保底选择。\"\n        },\n        {\n            \"school_name\": \"中国科学技术大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"中科大计算机专业竞争极为激烈，分数线为363分，学生预估分数低于13分。该校计算机专业全国顶尖，报考难度极大。\",\n            \"suggest\": \"需大幅提升各科成绩，尤其是数学和专业课，目标总分380分以上。\",\n            \"reason\": \"作为学生的梦想院校，虽然目前分数有差距，但通过努力仍有可能。建议作为冲刺目标。\"\n        },\n        {\n            \"school_name\": \"北京科技大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"北京科技大学计算机专业竞争激烈，分数线为369分，学生预估分数低于19分。该校计算机专业实力强劲，报考热度高。\",\n            \"suggest\": \"需全面提升各科成绩，尤其是政治和英语，目标总分370分以上。\",\n            \"reason\": \"北科大计算机专业实力雄厚，虽然目前分数有差距，但作为北京高校就业前景极佳，可作挑战目标。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"reason\": \"合工大计算机专业实力强劲，在安徽省内认可度高，学生预估分数高出分数线17分，录取概率大。学校性价比高，生活成本较低，且距离学生本科院校近，适应难度小。就业前景良好，是综合考量下的最佳选择。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":951,"completion_tokens":1285,"total_tokens":2236,"prompt_tokens_details":{"cached_tokens":896},"prompt_cache_hit_tokens":896,"prompt_cache_miss_tokens":55},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 13:52:08] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 13:52:08] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "南京理工大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，略高于学生预估总分350分。该校在江苏省内声誉良好，计算机专业实力较强，报考人数较多，但录取比例相对合理。",
            "suggest": "建议加强专业课复习，尤其是数据结构与算法，同时提升数学成绩至100分以上以增加竞争力。",
            "reason": "南京理工大学计算机专业实力雄厚，地理位置优越，就业前景好。学生预估分数接近分数线，有较大录取机会。"
        },
        {
            "school_name": "北京邮电大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "北京邮电大学计算机专业竞争非常激烈，分数线为340分，但实际录取分数通常更高。该校计算机专业在全国排名靠前，报考人数众多，竞争压力大。",
            "suggest": "建议将总分目标提升至360分以上，重点突破专业课和数学，同时提前联系导师。",
            "reason": "北邮计算机专业声誉极高，毕业生就业前景极佳。学生预估分数略高于分数线，但需进一步提升以应对激烈竞争。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "合肥工业大学计算机专业竞争适中，分数线为333分，学生预估分数高出17分。该校在安徽省内认可度高，计算机专业实力较强，竞争相对温和。",
            "suggest": "保持当前复习节奏，重点巩固专业课知识，尤其是操作系统和计算机网络。",
            "reason": "合工大计算机专业性价比高，学生预估分数优势明显，录取概率大，且离家近方便就读。"
        },
        {
            "school_name": "苏州大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "苏州大学计算机专业竞争较为温和，分数线为330分，学生预估分数高出20分。该校计算机专业发展迅速，但报考热度相对低于一线城市高校。",
            "suggest": "维持现有分数水平，重点关注专业课历年真题，提升编程实践能力。",
            "reason": "苏州大学计算机专业实力不断提升，地理位置优越，学生分数优势明显，录取把握大。"
        },
        {
            "school_name": "江南大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "江南大学计算机专业竞争相对较小，分数线为326分，学生预估分数高出24分。该校计算机专业并非王牌专业，报考热度适中。",
            "suggest": "可适当降低复习强度，但仍需保持专业课和数学的稳定发挥。",
            "reason": "江南大学录取门槛相对较低，学生分数优势显著，适合求稳选择，且无锡地理位置优越。"
        },
        {
            "school_name": "安徽大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "安徽大学计算机专业竞争较小，分数线为324分，学生预估分数高出26分。作为母校，录取时可能有一定优势。",
            "suggest": "可重点准备复试内容，尤其是项目经验和专业英语。",
            "reason": "安大计算机专业录取概率极高，母校背景可能带来额外优势，且生活成本低。"
        },
        {
            "school_name": "河海大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "河海大学计算机专业竞争温和，分数线为320分，学生预估分数高出30分。该校以水利为特色，计算机专业报考热度较低。",
            "suggest": "保持现有复习计划，可适当增加编程实践时间。",
            "reason": "河海大学计算机专业录取把握极大，南京地理位置优越，适合追求稳妥录取的学生。"
        },
        {
            "school_name": "东华大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "东华大学计算机专业竞争较小，分数线为318分，学生预估分数高出32分。该校以纺织为特色，计算机专业报考人数有限。",
            "suggest": "重点准备复试环节，尤其是跨学科知识的应用。",
            "reason": "东华大学录取门槛低，上海地理位置优越，学生分数优势明显，适合保底选择。"
        },
        {
            "school_name": "中国科学技术大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "中科大计算机专业竞争极为激烈，分数线为363分，学生预估分数低于13分。该校计算机专业全国顶尖，报考难度极大。",
            "suggest": "需大幅提升各科成绩，尤其是数学和专业课，目标总分380分以上。",
            "reason": "作为学生的梦想院校，虽然目前分数有差距，但通过努力仍有可能。建议作为冲刺目标。"
        },
        {
            "school_name": "北京科技大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "北京科技大学计算机专业竞争激烈，分数线为369分，学生预估分数低于19分。该校计算机专业实力强劲，报考热度高。",
            "suggest": "需全面提升各科成绩，尤其是政治和英语，目标总分370分以上。",
            "reason": "北科大计算机专业实力雄厚，虽然目前分数有差距，但作为北京高校就业前景极佳，可作挑战目标。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "reason": "合工大计算机专业实力强劲，在安徽省内认可度高，学生预估分数高出分数线17分，录取概率大。学校性价比高，生活成本较低，且距离学生本科院校近，适应难度小。就业前景良好，是综合考量下的最佳选择。"
        }
    ]
}
``` [] []
[2025-05-26 13:52:08] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 13:52:08] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 13:52:08] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 13:52:09] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 13:52:09] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 13:52:10] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 13:52:10] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 13:52:10] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 13:52:11] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 13:52:11] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 13:52:12] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 13:52:12] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 13:52:12] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 13:52:12] default.INFO: 开始保存推荐院校数据到数据库，reportId: 121, studentId: 1 [] []
[2025-05-26 13:52:13] default.INFO: 已删除报告ID 121 的旧推荐数据 [] []
[2025-05-26 13:52:13] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 13:52:13] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 13:52:13] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 13:52:13] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","中国科学技术大学","北京科技大学"] [] []
[2025-05-26 13:52:14] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 14:15:26] default.INFO: 获取学生列表请求参数: {"name":"\u674e\u56db","page":"1","limit":"50"} [] []
[2025-05-26 14:15:35] default.ERROR: 127.0.0.1 GET localhost:8787/api/report?name=&school_name=&major_name=&class=&page=&limit=
TypeError: think\db\BaseQuery::paginate(): Argument #1 ($listRows) must be of type array|int|null, string given, called in G:\code\php\webman\ai_report_php\app\controller\ReportController.php on line 89 and defined in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\BaseQuery.php:829
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\ReportController.php(89): think\db\BaseQuery->paginate('')
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\ReportController->reportList(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #307)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(290): Swow\Coroutine::run(Object(Closure))
#12 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(152): Workerman\Events\Swow->safeCall(Object(Closure), Array)
#13 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#14 {main} [] []
[2025-05-26 14:15:43] default.ERROR: 127.0.0.1 GET localhost:8787/api/report?name=&school_name=&major_name=&class=&page=&limit=
TypeError: think\db\BaseQuery::paginate(): Argument #1 ($listRows) must be of type array|int|null, string given, called in G:\code\php\webman\ai_report_php\app\controller\ReportController.php on line 89 and defined in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\BaseQuery.php:829
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\ReportController.php(89): think\db\BaseQuery->paginate('')
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\ReportController->reportList(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(676): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #311)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(290): Swow\Coroutine::run(Object(Closure))
#12 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(152): Workerman\Events\Swow->safeCall(Object(Closure), Array)
#13 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#14 {main} [] []
[2025-05-26 14:16:08] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 14:19:05] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"57","undergraduateSchoolName":"河海大学","undergraduateMajor":"2930","undergraduateMajorName":"水利水电工程","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"浙江省,安徽省,江苏省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"60","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"370","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 14:19:05] default.INFO: 四级成绩: 481 [] []
[2025-05-26 14:19:05] default.INFO: 六级成绩: 510 [] []
[2025-05-26 14:19:05] default.INFO: 托福成绩:  [] []
[2025-05-26 14:19:05] default.INFO: 英语能力: good [] []
[2025-05-26 14:19:05] default.INFO: 地区倾向: A区 [] []
[2025-05-26 14:19:06] default.INFO: AI推荐学校请求参数: {"report_id":122} [] []
[2025-05-26 14:19:06] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","18873","18933","26523","26524","18874","23154","23155"] [] []
[2025-05-26 14:19:06] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(122)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #335)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 14:19:07] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 14:19:25] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 14:19:38] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 14:21:12] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,江苏省,浙江省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"60","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"370","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 14:21:12] default.INFO: 四级成绩: 481 [] []
[2025-05-26 14:21:12] default.INFO: 六级成绩: 510 [] []
[2025-05-26 14:21:12] default.INFO: 托福成绩:  [] []
[2025-05-26 14:21:12] default.INFO: 英语能力: good [] []
[2025-05-26 14:21:12] default.INFO: 地区倾向: A区 [] []
[2025-05-26 14:21:13] default.INFO: AI推荐学校请求参数: {"report_id":123} [] []
[2025-05-26 14:21:13] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","18873","18933","26523","26524","18874","23154","23155"] [] []
[2025-05-26 14:21:13] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(123)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #347)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 14:21:14] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 14:31:13] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 14:31:19] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 14:38:54] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 14:39:37] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,浙江省,江苏省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"380","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 14:39:37] default.INFO: 四级成绩: 481 [] []
[2025-05-26 14:39:37] default.INFO: 六级成绩: 510 [] []
[2025-05-26 14:39:37] default.INFO: 托福成绩:  [] []
[2025-05-26 14:39:37] default.INFO: 英语能力: good [] []
[2025-05-26 14:39:37] default.INFO: 地区倾向: A区 [] []
[2025-05-26 14:39:38] default.INFO: AI推荐学校请求参数: {"report_id":124} [] []
[2025-05-26 14:39:38] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","18873","18933","26523","26524","18874","23154","23155"] [] []
[2025-05-26 14:39:38] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":67},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":89},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":101},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":119}] [] []
[2025-05-26 14:39:40] default.INFO: 启动爬虫响应: {"task_id":"01205c6f-b676-413c-bcfb-21440bf7e1ff","status":"running","message":"任务已创建，正在后台处理","total":4} [] []
[2025-05-26 14:39:40] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 14:39:40] default.INFO: 上下文长度: 2832 字符 [] []
[2025-05-26 14:39:40] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 14:39:40] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：0分\n英语（英语一）：80分\n数学（数学一）：110分\n专业课：120分\n总分：380分\n\n目标偏好：\n目标区域：A区\n目标省份：安徽省,浙江省,江苏省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：380分\n- 目标区域：A区\n- 目标省份：安徽省,浙江省,江苏省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线67分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线89分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线101分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线119分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n浙江省：浙江海洋大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 14:40:18] default.INFO: DeepSeek API 请求总时间: 38.482010126114秒 [] []
[2025-05-26 14:40:18] default.INFO: DeepSeek API 响应大小: 3922字节 [] []
[2025-05-26 14:40:18] default.INFO: DeepSeek API 响应: {"id":"240fc654-35d2-43d3-880d-e087e136a3b3","object":"chat.completion","created":1748241579,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学作为211高校，水利工程专业竞争较为激烈，但学生预估总分380分，远超该校313分的分数线，竞争力较强。该校在江苏省内声誉良好，水利工程专业实力较强，但报考人数较多，需注意复试表现。\",\n            \"suggest\": \"备考时需注重专业课和数学的巩固，确保高分优势。同时，提前准备复试，尤其是专业知识和英语口语。\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且预估分数远超分数线，录取概率高。该校水利工程专业实力强，地理位置优越，适合学生发展。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学是安徽省内知名211高校，水利工程专业竞争相对适中。学生预估总分380分，远超该校291分的分数线，优势明显。该校专业实力较强，但报考热度略低于中国矿业大学。\",\n            \"suggest\": \"备考时需保持数学和专业课的高分，同时关注复试中的专业问题。建议提前联系导师，了解研究方向。\",\n            \"reason\": \"合肥工业大学水利工程专业实力强，且学生分数优势明显，录取概率高。该校在安徽省内就业前景良好，适合学生选择。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学虽非211，但水利工程专业实力不俗，竞争难度适中。学生预估总分380分，远超该校279分的分数线，优势显著。该校在江苏省内认可度较高，报考人数较多。\",\n            \"suggest\": \"备考时需确保数学和专业课的高分，同时注重英语一的发挥。复试前可多了解该校的研究方向和导师信息。\",\n            \"reason\": \"江苏大学水利工程专业实力强，且学生分数远超分数线，录取概率高。该校地理位置优越，适合学生在江苏省内发展。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争相对较小，学生预估总分380分，远超该校261分的分数线，优势明显。该校专业特色鲜明，但知名度略低于其他院校。\",\n            \"suggest\": \"备考时需保持各科均衡发挥，尤其是专业课和数学。复试前可多关注该校的海洋水利特色研究方向。\",\n            \"reason\": \"浙江海洋大学水利工程专业竞争较小，学生分数优势显著，录取概率高。该校在浙江省内就业前景良好，适合学生选择。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且预估分数远超分数线，录取概率高。该校水利工程专业实力强，地理位置优越，综合性价比高，适合学生发展。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":761,"completion_tokens":672,"total_tokens":1433,"prompt_tokens_details":{"cached_tokens":192},"prompt_cache_hit_tokens":192,"prompt_cache_miss_tokens":569},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 14:40:18] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 14:40:18] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学作为211高校，水利工程专业竞争较为激烈，但学生预估总分380分，远超该校313分的分数线，竞争力较强。该校在江苏省内声誉良好，水利工程专业实力较强，但报考人数较多，需注意复试表现。",
            "suggest": "备考时需注重专业课和数学的巩固，确保高分优势。同时，提前准备复试，尤其是专业知识和英语口语。",
            "reason": "中国矿业大学是学生的梦想院校，且预估分数远超分数线，录取概率高。该校水利工程专业实力强，地理位置优越，适合学生发展。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学是安徽省内知名211高校，水利工程专业竞争相对适中。学生预估总分380分，远超该校291分的分数线，优势明显。该校专业实力较强，但报考热度略低于中国矿业大学。",
            "suggest": "备考时需保持数学和专业课的高分，同时关注复试中的专业问题。建议提前联系导师，了解研究方向。",
            "reason": "合肥工业大学水利工程专业实力强，且学生分数优势明显，录取概率高。该校在安徽省内就业前景良好，适合学生选择。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学虽非211，但水利工程专业实力不俗，竞争难度适中。学生预估总分380分，远超该校279分的分数线，优势显著。该校在江苏省内认可度较高，报考人数较多。",
            "suggest": "备考时需确保数学和专业课的高分，同时注重英语一的发挥。复试前可多了解该校的研究方向和导师信息。",
            "reason": "江苏大学水利工程专业实力强，且学生分数远超分数线，录取概率高。该校地理位置优越，适合学生在江苏省内发展。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争相对较小，学生预估总分380分，远超该校261分的分数线，优势明显。该校专业特色鲜明，但知名度略低于其他院校。",
            "suggest": "备考时需保持各科均衡发挥，尤其是专业课和数学。复试前可多关注该校的海洋水利特色研究方向。",
            "reason": "浙江海洋大学水利工程专业竞争较小，学生分数优势显著，录取概率高。该校在浙江省内就业前景良好，适合学生选择。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "reason": "中国矿业大学是学生的梦想院校，且预估分数远超分数线，录取概率高。该校水利工程专业实力强，地理位置优越，综合性价比高，适合学生发展。"
        }
    ]
}
``` [] []
[2025-05-26 14:40:18] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 14:40:18] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 14:40:19] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 14:40:19] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 14:40:19] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 14:40:20] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 14:40:20] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 14:40:20] default.INFO: 开始保存推荐院校数据到数据库，reportId: 124, studentId: 3 [] []
[2025-05-26 14:40:20] default.INFO: 已删除报告ID 124 的旧推荐数据 [] []
[2025-05-26 14:40:20] default.INFO: 推荐院校数据保存完成，共插入 5 条记录 [] []
[2025-05-26 14:40:21] default.INFO: 学校已存在，跳过: 中国矿业大学 [] []
[2025-05-26 14:40:21] default.INFO: 最终学校列表数量: 4 [] []
[2025-05-26 14:40:21] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","江苏大学","浙江海洋大学"] [] []
[2025-05-26 14:40:21] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 14:58:17] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:00:37] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"380","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:00:37] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:00:37] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:00:37] default.INFO: 托福成绩:  [] []
[2025-05-26 15:00:37] default.INFO: 英语能力: good [] []
[2025-05-26 15:00:37] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:00:38] default.INFO: AI推荐学校请求参数: {"report_id":125} [] []
[2025-05-26 15:00:38] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","18873","18933","26523","26524","18874","23154","23155"] [] []
[2025-05-26 15:00:38] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":67},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":89},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":101},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":119}] [] []
[2025-05-26 15:00:38] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 15:00:38] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 15:00:38] default.INFO: 上下文长度: 2832 字符 [] []
[2025-05-26 15:00:38] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 15:00:38] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：0分\n英语（英语一）：80分\n数学（数学一）：110分\n专业课：120分\n总分：380分\n\n目标偏好：\n目标区域：A区\n目标省份：江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：380分\n- 目标区域：A区\n- 目标省份：江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线67分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线89分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线101分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线119分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n浙江省：浙江海洋大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 15:01:06] default.INFO: DeepSeek API 请求总时间: 27.945369958878秒 [] []
[2025-05-26 15:01:06] default.INFO: DeepSeek API 响应大小: 3237字节 [] []
[2025-05-26 15:01:06] default.INFO: DeepSeek API 响应: {"id":"8e56fe95-46d5-46d6-b416-734cfc978d62","object":"chat.completion","created":1748242837,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学水利工程专业竞争较为激烈，作为211高校，报考人数较多，但学生预估分数380分远超313分的分数线，竞争力较强。\",\n            \"suggest\": \"保持当前复习节奏，重点关注专业课和数学的强化训练，确保高分优势。\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的地域偏好。学校水利工程专业实力雄厚，380分的预估分数有较大录取把握。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学水利工程专业竞争适中，分数线为291分，学生预估分数高出89分，录取概率很高。\",\n            \"suggest\": \"巩固数学和专业课基础，适当提升英语和政治分数以增加竞争力。\",\n            \"reason\": \"合肥工业大学是211高校，位于安徽省，符合学生目标区域。水利工程专业实力较强，且学生分数优势明显。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学水利工程专业竞争相对较小，分数线为279分，学生预估分数高出101分，录取可能性极大。\",\n            \"suggest\": \"保持稳定复习，重点提升政治和英语分数，争取更高总分。\",\n            \"reason\": \"江苏大学位于江苏省，水利工程专业实力不俗，学生分数远超分数线，是稳妥的选择。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争较小，分数线为261分，学生预估分数高出119分，录取几乎无悬念。\",\n            \"suggest\": \"重点复习专业课和数学，确保高分，同时提升英语和政治分数。\",\n            \"reason\": \"浙江海洋大学位于浙江省，符合学生地域偏好。学生分数远超分数线，录取概率极高。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"合肥工业大学是211高校，水利工程专业实力强，位于安徽省，符合学生目标区域。学生预估分数远超分数线，录取概率高且性价比突出。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":761,"completion_tokens":533,"total_tokens":1294,"prompt_tokens_details":{"cached_tokens":256},"prompt_cache_hit_tokens":256,"prompt_cache_miss_tokens":505},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 15:01:06] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 15:01:06] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学水利工程专业竞争较为激烈，作为211高校，报考人数较多，但学生预估分数380分远超313分的分数线，竞争力较强。",
            "suggest": "保持当前复习节奏，重点关注专业课和数学的强化训练，确保高分优势。",
            "reason": "中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的地域偏好。学校水利工程专业实力雄厚，380分的预估分数有较大录取把握。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学水利工程专业竞争适中，分数线为291分，学生预估分数高出89分，录取概率很高。",
            "suggest": "巩固数学和专业课基础，适当提升英语和政治分数以增加竞争力。",
            "reason": "合肥工业大学是211高校，位于安徽省，符合学生目标区域。水利工程专业实力较强，且学生分数优势明显。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学水利工程专业竞争相对较小，分数线为279分，学生预估分数高出101分，录取可能性极大。",
            "suggest": "保持稳定复习，重点提升政治和英语分数，争取更高总分。",
            "reason": "江苏大学位于江苏省，水利工程专业实力不俗，学生分数远超分数线，是稳妥的选择。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争较小，分数线为261分，学生预估分数高出119分，录取几乎无悬念。",
            "suggest": "重点复习专业课和数学，确保高分，同时提升英语和政治分数。",
            "reason": "浙江海洋大学位于浙江省，符合学生地域偏好。学生分数远超分数线，录取概率极高。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "reason": "合肥工业大学是211高校，水利工程专业实力强，位于安徽省，符合学生目标区域。学生预估分数远超分数线，录取概率高且性价比突出。"
        }
    ]
}
``` [] []
[2025-05-26 15:01:06] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 15:01:06] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 15:01:06] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 15:01:07] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 15:01:07] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 15:01:08] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 15:01:08] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 15:01:08] default.INFO: 开始保存推荐院校数据到数据库，reportId: 125, studentId: 3 [] []
[2025-05-26 15:01:08] default.INFO: 已删除报告ID 125 的旧推荐数据 [] []
[2025-05-26 15:01:08] default.INFO: 推荐院校数据保存完成，共插入 5 条记录 [] []
[2025-05-26 15:01:08] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 15:01:08] default.INFO: 最终学校列表数量: 4 [] []
[2025-05-26 15:01:08] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","江苏大学","浙江海洋大学"] [] []
[2025-05-26 15:01:09] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 15:21:30] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:22:38] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 15:25:46] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 15:26:25] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"380","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:26:26] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:26:26] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:26:26] default.INFO: 托福成绩:  [] []
[2025-05-26 15:26:26] default.INFO: 英语能力: good [] []
[2025-05-26 15:26:26] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:26:26] default.INFO: AI推荐学校请求参数: {"report_id":126} [] []
[2025-05-26 15:26:27] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 15:26:27] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(126)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/remote/ai_...', 'POST/api/remote...', Object(support\Request), 200)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #289)
#11 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#12 {main} [] []
[2025-05-26 15:26:27] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 15:28:05] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 15:29:09] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"80","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:29:10] default.INFO: 四级成绩: 460 [] []
[2025-05-26 15:29:10] default.INFO: 六级成绩: 500 [] []
[2025-05-26 15:29:10] default.INFO: 托福成绩:  [] []
[2025-05-26 15:29:10] default.INFO: 英语能力: 一般 [] []
[2025-05-26 15:29:10] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:29:25] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:29:39] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 15:29:47] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:30:16] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"380","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:30:16] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:30:16] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:30:16] default.INFO: 托福成绩:  [] []
[2025-05-26 15:30:16] default.INFO: 英语能力: good [] []
[2025-05-26 15:30:16] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:30:17] default.INFO: AI推荐学校请求参数: {"report_id":128} [] []
[2025-05-26 15:30:17] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 15:30:17] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(128)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/remote/ai_...', 'POST/api/remote...', Object(support\Request), 200)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #294)
#11 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#12 {main} [] []
[2025-05-26 15:30:18] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 15:35:21] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 15:35:41] default.INFO: 获取学生列表请求参数: {"name":"\u7ad9","page":"1","limit":"10"} [] []
[2025-05-26 15:35:43] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:36:17] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"70","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"380","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:36:17] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:36:17] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:36:17] default.INFO: 托福成绩:  [] []
[2025-05-26 15:36:17] default.INFO: 英语能力: good [] []
[2025-05-26 15:36:17] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:36:18] default.INFO: AI推荐学校请求参数: {"report_id":129} [] []
[2025-05-26 15:36:18] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 15:36:18] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(129)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/remote/ai_...', 'POST/api/remote...', Object(support\Request), 200)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #295)
#11 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#12 {main} [] []
[2025-05-26 15:36:19] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 15:39:19] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 15:43:03] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"80","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politicsScore":"72","englishType":"英语一","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"382","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:43:03] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:43:03] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:43:03] default.INFO: 托福成绩:  [] []
[2025-05-26 15:43:03] default.INFO: 英语能力: good [] []
[2025-05-26 15:43:03] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:43:16] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 15:43:36] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"140","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"387","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:43:36] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:43:36] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:43:36] default.INFO: 托福成绩:  [] []
[2025-05-26 15:43:36] default.INFO: 英语能力: good [] []
[2025-05-26 15:43:36] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:43:36] default.ERROR: toggleAIOverlay 异常: Undefined array key "politicsScore" in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 994 [] []
[2025-05-26 15:43:36] default.ERROR: 127.0.0.1 POST localhost:8787/api/student/toggle-ai-overlay
ErrorException: Undefined variable $report in G:\code\php\webman\ai_report_php\app\controller\StudentController.php:1117
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\StudentController.php(1117): {closure}(2, 'Undefined varia...', 'G:\\code\\php\\web...', 1117)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\StudentController->toggleAIOverlay(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #291)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 15:43:52] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"140","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"387","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 15:43:52] default.INFO: 四级成绩: 481 [] []
[2025-05-26 15:43:52] default.INFO: 六级成绩: 510 [] []
[2025-05-26 15:43:52] default.INFO: 托福成绩:  [] []
[2025-05-26 15:43:52] default.INFO: 英语能力: good [] []
[2025-05-26 15:43:52] default.INFO: 地区倾向: A区 [] []
[2025-05-26 15:43:52] default.ERROR: toggleAIOverlay 异常: Undefined array key "politicsScore" in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 994 [] []
[2025-05-26 15:43:52] default.ERROR: 127.0.0.1 POST localhost:8787/api/student/toggle-ai-overlay
ErrorException: Undefined variable $report in G:\code\php\webman\ai_report_php\app\controller\StudentController.php:1117
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\StudentController.php(1117): {closure}(2, 'Undefined varia...', 'G:\\code\\php\\web...', 1117)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\StudentController->toggleAIOverlay(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #295)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 16:08:42] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"140","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"387","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:08:42] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:08:42] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:08:42] default.INFO: 托福成绩:  [] []
[2025-05-26 16:08:42] default.INFO: 英语能力: good [] []
[2025-05-26 16:08:42] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:08:42] default.ERROR: toggleAIOverlay 异常: Undefined array key "politicsScore" in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 994 [] []
[2025-05-26 16:08:42] default.ERROR: 127.0.0.1 POST localhost:8787/api/student/toggle-ai-overlay
ErrorException: Undefined variable $report in G:\code\php\webman\ai_report_php\app\controller\StudentController.php:1117
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\StudentController.php(1117): {closure}(2, 'Undefined varia...', 'G:\\code\\php\\web...', 1117)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\StudentController->toggleAIOverlay(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #296)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 16:09:41] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"140","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"387","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:09:42] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:09:42] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:09:42] default.INFO: 托福成绩:  [] []
[2025-05-26 16:09:42] default.INFO: 英语能力: good [] []
[2025-05-26 16:09:42] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:09:42] default.ERROR: toggleAIOverlay 异常: Undefined array key "politicsScore" in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 994 [] []
[2025-05-26 16:09:42] default.ERROR: 127.0.0.1 POST localhost:8787/api/student/toggle-ai-overlay
ErrorException: Undefined variable $report in G:\code\php\webman\ai_report_php\app\controller\StudentController.php:1117
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\StudentController.php(1117): {closure}(2, 'Undefined varia...', 'G:\\code\\php\\web...', 1117)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\StudentController->toggleAIOverlay(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(676): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #297)
#10 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#11 {main} [] []
[2025-05-26 16:11:33] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 16:12:05] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"387","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:12:05] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:12:05] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:12:05] default.INFO: 托福成绩:  [] []
[2025-05-26 16:12:05] default.INFO: 英语能力: good [] []
[2025-05-26 16:12:05] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:12:06] default.INFO: AI推荐学校请求参数: {"report_id":131} [] []
[2025-05-26 16:12:06] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 16:12:06] default.ERROR: 127.0.0.1 POST localhost:8787/api/remote/ai_recommendation
Error: Call to a member function toArray() on null in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php:937
Stack trace:
#0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(131)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/remote/ai_...', 'POST/api/remote...', Object(support\Request), 200)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #288)
#11 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#12 {main} [] []
[2025-05-26 16:12:07] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 16:13:16] default.ERROR: 127.0.0.1 GET localhost:8787/api/student/detail?id=3
think\db\exception\PDOException: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php:842
Stack trace:
#0 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(441): think\db\connector\Mysql->getFields('`ba_student`')
#2 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(398): think\db\PDOConnection->getTableFieldsInfo('ba_student')
#3 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('123.56.183.51_3...', 'ba_student', false)
#4 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(427): think\db\PDOConnection->getSchemaInfo('ba_student')
#5 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(516): think\db\PDOConnection->getTableInfo('ba_student', 'type')
#6 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('ba_student')
#7 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\DbConnect.php(92): think\db\Query->getFieldsType()
#8 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\Attribute.php(47): think\Model->getFields()
#9 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\Model.php(151): think\Model->initializeData(Array)
#10 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\DbConnect.php(192): think\Model->__construct()
#11 G:\code\php\webman\ai_report_php\app\controller\StudentController.php(275): think\Model::__callStatic('where', Array)
#12 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\StudentController->getDetail(Object(support\Request))
#13 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#15 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#16 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#17 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#18 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#19 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/student/de...', 'GET/api/student...', Object(support\Request), 200)
#20 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#21 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #246)
#22 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#23 {main} [] []
[2025-05-26 16:13:36] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 16:14:07] default.INFO: toggleAIOverlay 请求参数: {"name":"张三","sex":"1","phone":"13083409461","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"计算机科学与技术","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"undergraduateTranscript":[{"id":1,"title":"高数","score":"89"},{"id":2,"title":"英语","score":"90"},{"id":3,"title":"专业课一","score":"70"}],"englishScore":"144","cet4":"460","cet6":"500","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,上海市,北京市,江苏省,浙江省","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"90","professionalScore":"110","totalScore":"350","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:14:07] default.INFO: 四级成绩: 460 [] []
[2025-05-26 16:14:07] default.INFO: 六级成绩: 500 [] []
[2025-05-26 16:14:07] default.INFO: 托福成绩:  [] []
[2025-05-26 16:14:07] default.INFO: 英语能力: 一般 [] []
[2025-05-26 16:14:07] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:14:08] default.INFO: AI推荐学校请求参数: {"report_id":132} [] []
[2025-05-26 16:14:08] default.INFO: 获取数据库中匹配的院校["75577","75674","18764","18774","75583","75681","75584","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","84649"] [] []
[2025-05-26 16:14:08] default.INFO: 梦想院校 [] []
[2025-05-26 16:14:08] default.INFO: 梦想院校{"id":"26439","province":"\u5b89\u5fbd\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0812","second_level_name":"(081200)\u8ba1\u7b97\u673a\u79d1\u5b66\u4e0e\u6280\u672f","school_name":"\u4e2d\u56fd\u79d1\u5b66\u6280\u672f\u5927\u5b66","major_code":"081200","major_name":"081200\u8ba1\u7b97\u673a\u79d1\u5b66\u4e0e\u6280\u672f","area":"\u5b89\u5fbd\u7701","school_type":"985,211,\u53cc\u4e00\u6d41","college":"\u8ba1\u7b97\u673a\u79d1\u5b66\u4e0e\u6280\u672f..","study_type":"\u5168\u65e5\u5236","warning_level":"10:31","ranking":"10:262","admission":"\u603b\u5f55:31  \u4e00\u5fd7\u613f:31   \u8c03\u5242:0  \u5fc5\u8fbe\u5206:363","course_suggestion":"\u653f:73  \u96be   \u82f1:84  \u96be   \u4e13\u4e00:116  \u96be   \u4e13\u4e8c:122  \u7b80","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=QPcdt2IbXvrtA83TSsRWskkjrOyCvm","detail_url_status":1,"created_at":"2025-05-06 13:53:32","must_reach_score":363,"crawl_start_time":"2025-05-23 17:42:36","crawl_end_time":"2025-05-23 17:42:36"} [] []
[2025-05-26 16:14:08] default.INFO: 学校列表: [{"school_id":"75577","school_name":"北京科技大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":369,"score_diff":-19},{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":6},{"school_id":"75583","school_name":"北京邮电大学","province":"北京市","area":"北京市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":340,"score_diff":10},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":17},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":20},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":24},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":26},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":30},{"school_id":"84649","school_name":"东华大学","province":"上海市","area":"上海市","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":318,"score_diff":32},{"school_id":"26439","school_name":"中国科学技术大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":363,"score_diff":-13}] [] []
[2025-05-26 16:14:09] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 16:14:09] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 16:14:09] default.INFO: 上下文长度: 3734 字符 [] []
[2025-05-26 16:14:09] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 16:14:09] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张三\n性别：男\n本科院校：安徽大学\n本科专业：计算机科学与技术\n目标专业：(081200)计算机科学与技术（专业代码：081200）\n是否跨专业：否\n\n考试成绩预估：\n政治：70分\n英语（英语一）：144分\n数学（数学一）：90分\n专业课：110分\n总分：350分\n\n目标偏好：\n目标区域：A区\n目标省份：安徽省,上海市,北京市,江苏省,浙江省\n院校层次：211\n梦想院校：中国科学技术大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081200\n- 预估总分：350分\n- 目标区域：A区\n- 目标省份：安徽省,上海市,北京市,江苏省,浙江省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 北京科技大学（北京市）- 081200计算机科学与技术，分数线：369分，低于分数线19分\n2. 南京理工大学（江苏省）- 081200计算机科学与技术，分数线：344分，高出分数线6分\n3. 北京邮电大学（北京市）- 081200计算机科学与技术，分数线：340分，高出分数线10分\n4. 合肥工业大学（安徽省）- 081200计算机科学与技术，分数线：333分，高出分数线17分\n5. 苏州大学（江苏省）- 081200计算机科学与技术，分数线：330分，高出分数线20分\n6. 江南大学（江苏省）- 081200计算机科学与技术，分数线：326分，高出分数线24分\n7. 安徽大学（安徽省）- 081200计算机科学与技术，分数线：324分，高出分数线26分\n8. 河海大学（江苏省）- 081200计算机科学与技术，分数线：320分，高出分数线30分\n9. 东华大学（上海市）- 081200计算机科学与技术，分数线：318分，高出分数线32分\n\n【985,211,双一流类院校】\n1. 中国科学技术大学（安徽省）- 081200计算机科学与技术，分数线：363分，低于分数线13分\n\n【按省份分类】\n北京市：北京科技大学、北京邮电大学\n江苏省：南京理工大学、苏州大学、江南大学、河海大学\n安徽省：合肥工业大学、安徽大学、中国科学技术大学\n上海市：东华大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 16:15:18] default.INFO: DeepSeek API 请求总时间: 68.949434995651秒 [] []
[2025-05-26 16:15:18] default.INFO: DeepSeek API 响应大小: 8006字节 [] []
[2025-05-26 16:15:18] default.INFO: DeepSeek API 响应: 
{"id":"4c12fa9b-f0d2-4dba-a114-52f73d010118","object":"chat.completion","created":1748247248,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"南京理工大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，略高于学生预估总分350分。该校在江苏省内享有较高声誉，计算机专业实力较强，报考人数较多，但录取比例相对合理。\",\n            \"suggest\": \"建议加强数学和专业课的复习，争取数学一提高到100分以上，专业课提高到120分以上，以增加录取机会。\",\n            \"reason\": \"南京理工大学计算机专业实力强，地理位置优越，就业前景好。学生预估总分略高于分数线，有一定竞争力，适合作为冲刺目标。\"\n        },\n        {\n            \"school_name\": \"北京邮电大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"北京邮电大学计算机科学与技术专业竞争非常激烈，分数线为340分，学生预估总分350分略高于分数线。该校计算机专业全国排名靠前，报考人数众多，录取难度较大。\",\n            \"suggest\": \"建议重点提升数学和专业课成绩，数学一目标100分，专业课目标120分，同时加强政治和英语的稳定性。\",\n            \"reason\": \"北京邮电大学计算机专业实力顶尖，就业前景极佳。学生预估总分略高于分数线，适合作为冲刺目标，但需进一步提升单科成绩。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"合肥工业大学计算机科学与技术专业竞争适中，分数线为333分，学生预估总分350分明显高于分数线。该校计算机专业在安徽省内排名靠前，报考人数较多，但录取比例较高。\",\n            \"suggest\": \"建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。\",\n            \"reason\": \"合肥工业大学计算机专业实力较强，且学生预估总分明显高于分数线，录取概率较大，适合作为稳妥选择。\"\n        },\n        {\n            \"school_name\": \"苏州大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"苏州大学计算机科学与技术专业竞争较为激烈，分数线为330分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定影响力，报考人数较多。\",\n            \"suggest\": \"建议保持数学和专业课的复习强度，争取数学一达到95分以上，专业课达到115分以上。\",\n            \"reason\": \"苏州大学计算机专业实力不错，地理位置优越，学生预估总分优势明显，适合作为稳妥选择。\"\n        },\n        {\n            \"school_name\": \"江南大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"江南大学计算机科学与技术专业竞争适中，分数线为326分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定声誉，报考人数适中。\",\n            \"suggest\": \"建议保持当前复习节奏，重点巩固专业课知识，确保总分稳定在350分以上。\",\n            \"reason\": \"江南大学计算机专业实力尚可，学生预估总分优势明显，录取概率较大，适合作为保底选择。\"\n        },\n        {\n            \"school_name\": \"安徽大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"安徽大学计算机科学与技术专业竞争适中，分数线为324分，学生预估总分350分明显高于分数线。该校计算机专业在安徽省内有一定影响力，报考人数较多。\",\n            \"suggest\": \"建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。\",\n            \"reason\": \"安徽大学是学生的本科院校，熟悉环境和师资，录取概率较大，适合作为保底选择。\"\n        },\n        {\n            \"school_name\": \"河海大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"河海大学计算机科学与技术专业竞争较为缓和，分数线为320分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定声誉，报考人数适中。\",\n            \"suggest\": \"建议保持当前复习节奏，重点巩固专业课知识，确保总分稳定在350分以上。\",\n            \"reason\": \"河海大学计算机专业实力尚可，学生预估总分优势明显，录取概率较大，适合作为保底选择。\"\n        },\n        {\n            \"school_name\": \"东华大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"东华大学计算机科学与技术专业竞争较为缓和，分数线为318分，学生预估总分350分明显高于分数线。该校计算机专业在上海市内有一定影响力，报考人数适中。\",\n            \"suggest\": \"建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。\",\n            \"reason\": \"东华大学位于上海，地理位置优越，学生预估总分优势明显，适合作为保底选择。\"\n        },\n        {\n            \"school_name\": \"中国科学技术大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"中国科学技术大学计算机科学与技术专业竞争非常激烈，分数线为363分，学生预估总分350分略低于分数线。该校计算机专业全国顶尖，报考人数众多，录取难度极大。\",\n            \"suggest\": \"建议大幅提升数学和专业课成绩，数学一目标110分，专业课目标130分，同时确保政治和英语稳定发挥。\",\n            \"reason\": \"中国科学技术大学是学生的梦想院校，计算机专业实力顶尖，但录取难度极大，适合作为冲刺目标，需大幅提升成绩。\"\n        },\n        {\n            \"school_name\": \"北京科技大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"北京科技大学计算机科学与技术专业竞争非常激烈，分数线为369分，学生预估总分350分低于分数线。该校计算机专业全国排名靠前，报考人数众多，录取难度较大。\",\n            \"suggest\": \"建议大幅提升数学和专业课成绩，数学一目标110分，专业课目标130分，同时确保政治和英语稳定发挥。\",\n            \"reason\": \"北京科技大学计算机专业实力强，但学生预估总分低于分数线，需大幅提升成绩才有机会，适合作为冲刺目标。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"reason\": \"合肥工业大学计算机专业实力较强，学生预估总分明显高于分数线，录取概率大，且位于安徽省，地理位置符合学生偏好，综合性价比高。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":951,"completion_tokens":1415,"total_tokens":2366,"prompt_tokens_details":{"cached_tokens":256},"prompt_cache_hit_tokens":256,"prompt_cache_miss_tokens":695},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 16:15:18] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 16:15:18] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "南京理工大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，略高于学生预估总分350分。该校在江苏省内享有较高声誉，计算机专业实力较强，报考人数较多，但录取比例相对合理。",
            "suggest": "建议加强数学和专业课的复习，争取数学一提高到100分以上，专业课提高到120分以上，以增加录取机会。",
            "reason": "南京理工大学计算机专业实力强，地理位置优越，就业前景好。学生预估总分略高于分数线，有一定竞争力，适合作为冲刺目标。"
        },
        {
            "school_name": "北京邮电大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "北京邮电大学计算机科学与技术专业竞争非常激烈，分数线为340分，学生预估总分350分略高于分数线。该校计算机专业全国排名靠前，报考人数众多，录取难度较大。",
            "suggest": "建议重点提升数学和专业课成绩，数学一目标100分，专业课目标120分，同时加强政治和英语的稳定性。",
            "reason": "北京邮电大学计算机专业实力顶尖，就业前景极佳。学生预估总分略高于分数线，适合作为冲刺目标，但需进一步提升单科成绩。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "合肥工业大学计算机科学与技术专业竞争适中，分数线为333分，学生预估总分350分明显高于分数线。该校计算机专业在安徽省内排名靠前，报考人数较多，但录取比例较高。",
            "suggest": "建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。",
            "reason": "合肥工业大学计算机专业实力较强，且学生预估总分明显高于分数线，录取概率较大，适合作为稳妥选择。"
        },
        {
            "school_name": "苏州大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "苏州大学计算机科学与技术专业竞争较为激烈，分数线为330分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定影响力，报考人数较多。",
            "suggest": "建议保持数学和专业课的复习强度，争取数学一达到95分以上，专业课达到115分以上。",
            "reason": "苏州大学计算机专业实力不错，地理位置优越，学生预估总分优势明显，适合作为稳妥选择。"
        },
        {
            "school_name": "江南大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "江南大学计算机科学与技术专业竞争适中，分数线为326分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定声誉，报考人数适中。",
            "suggest": "建议保持当前复习节奏，重点巩固专业课知识，确保总分稳定在350分以上。",
            "reason": "江南大学计算机专业实力尚可，学生预估总分优势明显，录取概率较大，适合作为保底选择。"
        },
        {
            "school_name": "安徽大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "安徽大学计算机科学与技术专业竞争适中，分数线为324分，学生预估总分350分明显高于分数线。该校计算机专业在安徽省内有一定影响力，报考人数较多。",
            "suggest": "建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。",
            "reason": "安徽大学是学生的本科院校，熟悉环境和师资，录取概率较大，适合作为保底选择。"
        },
        {
            "school_name": "河海大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "河海大学计算机科学与技术专业竞争较为缓和，分数线为320分，学生预估总分350分明显高于分数线。该校计算机专业在江苏省内有一定声誉，报考人数适中。",
            "suggest": "建议保持当前复习节奏，重点巩固专业课知识，确保总分稳定在350分以上。",
            "reason": "河海大学计算机专业实力尚可，学生预估总分优势明显，录取概率较大，适合作为保底选择。"
        },
        {
            "school_name": "东华大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "东华大学计算机科学与技术专业竞争较为缓和，分数线为318分，学生预估总分350分明显高于分数线。该校计算机专业在上海市内有一定影响力，报考人数适中。",
            "suggest": "建议保持当前复习节奏，重点巩固数学和专业课知识，确保总分稳定在350分以上。",
            "reason": "东华大学位于上海，地理位置优越，学生预估总分优势明显，适合作为保底选择。"
        },
        {
            "school_name": "中国科学技术大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "中国科学技术大学计算机科学与技术专业竞争非常激烈，分数线为363分，学生预估总分350分略低于分数线。该校计算机专业全国顶尖，报考人数众多，录取难度极大。",
            "suggest": "建议大幅提升数学和专业课成绩，数学一目标110分，专业课目标130分，同时确保政治和英语稳定发挥。",
            "reason": "中国科学技术大学是学生的梦想院校，计算机专业实力顶尖，但录取难度极大，适合作为冲刺目标，需大幅提升成绩。"
        },
        {
            "school_name": "北京科技大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "北京科技大学计算机科学与技术专业竞争非常激烈，分数线为369分，学生预估总分350分低于分数线。该校计算机专业全国排名靠前，报考人数众多，录取难度较大。",
            "suggest": "建议大幅提升数学和专业课成绩，数学一目标110分，专业课目标130分，同时确保政治和英语稳定发挥。",
            "reason": "北京科技大学计算机专业实力强，但学生预估总分低于分数线，需大幅提升成绩才有机会，适合作为冲刺目标。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "reason": "合肥工业大学计算机专业实力较强，学生预估总分明显高于分数线，录取概率大，且位于安徽省，地理位置符合学生偏好，综合性价比高。"
        }
    ]
}
``` [] []
[2025-05-26 16:15:18] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 16:15:18] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 16:15:18] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 16:15:19] default.INFO: 找到学校数据: 北京邮电大学, ID: 69, logo:  [] []
[2025-05-26 16:15:19] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 16:15:19] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 16:15:20] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 16:15:20] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 16:15:20] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 16:15:21] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-05-26 16:15:21] default.INFO: 找到学校数据: 中国科学技术大学, ID: 14, logo:  [] []
[2025-05-26 16:15:21] default.INFO: 找到学校数据: 北京科技大学, ID: 64, logo:  [] []
[2025-05-26 16:15:22] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 16:15:22] default.INFO: 开始保存推荐院校数据到数据库，reportId: 132, studentId: 1 [] []
[2025-05-26 16:15:22] default.INFO: 已删除报告ID 132 的旧推荐数据 [] []
[2025-05-26 16:15:22] default.INFO: 推荐院校数据保存完成，共插入 11 条记录 [] []
[2025-05-26 16:15:23] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 16:15:23] default.INFO: 最终学校列表数量: 10 [] []
[2025-05-26 16:15:23] default.INFO: 最终学校列表名称: ["南京理工大学","北京邮电大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","东华大学","中国科学技术大学","北京科技大学"] [] []
[2025-05-26 16:15:23] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 16:15:43] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 16:15:59] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 16:16:23] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:16:23] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:16:23] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:16:23] default.INFO: 托福成绩:  [] []
[2025-05-26 16:16:23] default.INFO: 英语能力: good [] []
[2025-05-26 16:16:23] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:16:24] default.INFO: AI推荐学校请求参数: {"report_id":133} [] []
[2025-05-26 16:16:24] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 16:16:24] default.INFO: 梦想院校 [] []
[2025-05-26 16:16:24] default.INFO: 梦想院校null [] []
[2025-05-26 16:16:24] default.ERROR: 构建AI上下文异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php on line 941 [] []
[2025-05-26 16:16:24] default.ERROR: DeepSeek API 调用异常: 构建AI上下文异常 [] []
[2025-05-26 16:16:24] default.ERROR: 异常堆栈: #0 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#4 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#5 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #310)
#9 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#10 {main} [] []
[2025-05-26 16:16:24] default.INFO: 开始保存推荐院校数据到数据库，reportId: 133, studentId: 3 [] []
[2025-05-26 16:16:24] default.INFO: 已删除报告ID 133 的旧推荐数据 [] []
[2025-05-26 16:16:24] default.WARNING: 没有推荐院校数据需要插入 [] []
[2025-05-26 16:16:24] default.INFO: 最终学校列表数量: 0 [] []
[2025-05-26 16:16:24] default.INFO: 最终学校列表名称: [] [] []
[2025-05-26 16:16:25] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 16:20:00] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 16:20:12] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 16:20:31] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:20:31] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:20:31] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:20:31] default.INFO: 托福成绩:  [] []
[2025-05-26 16:20:31] default.INFO: 英语能力: good [] []
[2025-05-26 16:20:31] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:20:32] default.INFO: AI推荐学校请求参数: {"report_id":134} [] []
[2025-05-26 16:20:32] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 16:20:32] default.INFO: 梦想院校 [] []
[2025-05-26 16:20:32] default.INFO: 梦想院校null [] []
[2025-05-26 16:20:32] default.ERROR: 构建AI上下文异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\RemoteController.php on line 943 [] []
[2025-05-26 16:20:32] default.ERROR: DeepSeek API 调用异常: 梦校没有改专业研究生招生计划 [] []
[2025-05-26 16:20:32] default.ERROR: 异常堆栈: #0 G:\code\php\webman\ai_report_php\app\controller\RemoteController.php(290): app\controller\RemoteController->buildAiContext(134)
#1 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\RemoteController->getAiRecommendation(Object(support\Request))
#2 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#4 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#5 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#6 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#7 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/remote/ai_...', 'POST/api/remote...', Object(support\Request), 200)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #292)
#11 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#12 {main} [] []
[2025-05-26 16:20:33] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 16:43:15] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:43:15] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:43:15] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:43:15] default.INFO: 托福成绩:  [] []
[2025-05-26 16:43:15] default.INFO: 英语能力: good [] []
[2025-05-26 16:43:15] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:43:15] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 982 [] []
[2025-05-26 16:47:36] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:47:36] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:47:37] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:47:37] default.INFO: 托福成绩:  [] []
[2025-05-26 16:47:37] default.INFO: 英语能力: good [] []
[2025-05-26 16:47:37] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:47:37] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 982 [] []
[2025-05-26 16:47:44] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 16:47:44] default.INFO: 四级成绩: 481 [] []
[2025-05-26 16:47:44] default.INFO: 六级成绩: 510 [] []
[2025-05-26 16:47:44] default.INFO: 托福成绩:  [] []
[2025-05-26 16:47:44] default.INFO: 英语能力: good [] []
[2025-05-26 16:47:44] default.INFO: 地区倾向: A区 [] []
[2025-05-26 16:47:45] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 982 [] []
[2025-05-26 17:05:57] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 17:07:38] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:07:41] default.ERROR: 获取学生列表异常: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php on line 842 [] []
[2025-05-26 17:07:47] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:07:48] default.ERROR: 127.0.0.1 GET localhost:8787/api/report?name=&school_name=&major_name=&class=&page=1&limit=10
think\db\exception\PDOException: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php:842
Stack trace:
#0 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(441): think\db\connector\Mysql->getFields('`ba_school_repo...')
#2 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(398): think\db\PDOConnection->getTableFieldsInfo('ba_school_repor...')
#3 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('123.56.183.51_3...', 'ba_school_repor...', false)
#4 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(427): think\db\PDOConnection->getSchemaInfo('ba_school_repor...')
#5 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(479): think\db\PDOConnection->getTableInfo('ba_school_repor...', 'pk')
#6 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\Query.php(325): think\db\PDOConnection->getPk('ba_school_repor...')
#7 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\concern\WhereQuery.php(43): think\db\Query->getPk()
#8 G:\code\php\webman\ai_report_php\app\controller\ReportController.php(87): think\db\BaseQuery->where('s.admin_id', 2)
#9 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\ReportController->reportList(Object(support\Request))
#10 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#11 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#12 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#13 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#14 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#15 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#16 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(676): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#17 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #304)
#18 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#19 {main} [] []
[2025-05-26 17:09:07] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:43:35] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:44:04] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:45:29] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:49:50] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:49:53] default.ERROR: 127.0.0.1 GET localhost:8787/api/tag/all
think\db\exception\PDOException: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php:842
Stack trace:
#0 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(441): think\db\connector\Mysql->getFields('`ba_tags`')
#2 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(398): think\db\PDOConnection->getTableFieldsInfo('ba_tags')
#3 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('123.56.183.51_3...', 'ba_tags', false)
#4 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(427): think\db\PDOConnection->getSchemaInfo('ba_tags')
#5 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php(516): think\db\PDOConnection->getTableInfo('ba_tags', 'type')
#6 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('ba_tags')
#7 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\DbConnect.php(92): think\db\Query->getFieldsType()
#8 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\Attribute.php(47): think\Model->getFields()
#9 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\Model.php(151): think\Model->initializeData(Array)
#10 G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\model\concern\DbConnect.php(192): think\Model->__construct()
#11 G:\code\php\webman\ai_report_php\app\model\Tag.php(33): think\Model::__callStatic('where', Array)
#12 G:\code\php\webman\ai_report_php\app\controller\TagController.php(23): app\model\Tag::getFirstLevelTags()
#13 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(336): app\controller\TagController->getAll(Object(support\Request))
#14 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#15 G:\code\php\webman\ai_report_php\app\middleware\JwtMiddleware.php(31): Webman\App::Webman\{closure}(Object(support\Request))
#16 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtMiddleware->process(Object(support\Request), Object(Closure))
#17 G:\code\php\webman\ai_report_php\app\middleware\CorsMiddleware.php(12): Webman\App::Webman\{closure}(Object(support\Request))
#18 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process(Object(support\Request), Object(Closure))
#19 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#20 G:\code\php\webman\ai_report_php\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/tag/all', 'GET/api/tag/all', Object(support\Request), 200)
#21 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Connection\TcpConnection.php(748): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#22 G:\code\php\webman\ai_report_php\vendor\workerman\workerman\src\Events\Swow.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #251)
#23 [internal function]: Workerman\Events\Swow->Workerman\Events\{closure}()
#24 {main} [] []
[2025-05-26 17:49:53] default.ERROR: 获取学生列表异常: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php on line 842 [] []
[2025-05-26 17:50:51] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:56:21] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:56:24] default.ERROR: 获取学生列表异常: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php on line 842 [] []
[2025-05-26 17:57:13] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:57:35] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:58:42] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 17:58:46] default.ERROR: 获取学生列表异常: SQLSTATE[HY000] [2002] Socket connect wait failed, reason: Timed out for 3000 ms in G:\code\php\webman\ai_report_php\vendor\topthink\think-orm\src\db\PDOConnection.php on line 842 [] []
[2025-05-26 18:01:27] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 18:01:42] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 18:01:44] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 18:04:51] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:04:51] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:04:51] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:04:51] default.INFO: 托福成绩:  [] []
[2025-05-26 18:04:51] default.INFO: 英语能力: good [] []
[2025-05-26 18:04:51] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:04:51] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 982 [] []
[2025-05-26 18:05:10] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:05:11] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:05:11] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:05:11] default.INFO: 托福成绩:  [] []
[2025-05-26 18:05:11] default.INFO: 英语能力: good [] []
[2025-05-26 18:05:11] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:05:11] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 982 [] []
[2025-05-26 18:07:48] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:07:48] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:07:48] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:07:48] default.INFO: 托福成绩:  [] []
[2025-05-26 18:07:48] default.INFO: 英语能力: good [] []
[2025-05-26 18:07:48] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:07:49] default.INFO: dreamSchoolInfoSql: SELECT * FROM `ba_school_info` WHERE  `school_name` = '中国科学技术大学'  AND `major_code` = '(081500)水利工程' LIMIT 1 [] []
[2025-05-26 18:07:49] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 983 [] []
[2025-05-26 18:08:24] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:08:24] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:08:24] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:08:24] default.INFO: 托福成绩:  [] []
[2025-05-26 18:08:24] default.INFO: 英语能力: good [] []
[2025-05-26 18:08:24] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:08:25] default.INFO: dreamSchoolInfoSql: SELECT * FROM `ba_school_info` WHERE  `school_name` = '中国科学技术大学'  AND `major_code` = '(081500)水利工程' LIMIT 1 [] []
[2025-05-26 18:08:25] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 983 [] []
[2025-05-26 18:08:33] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:08:33] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:08:33] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:08:33] default.INFO: 托福成绩:  [] []
[2025-05-26 18:08:33] default.INFO: 英语能力: good [] []
[2025-05-26 18:08:33] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:08:33] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 983 [] []
[2025-05-26 18:08:54] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:08:54] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:08:54] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:08:54] default.INFO: 托福成绩:  [] []
[2025-05-26 18:08:54] default.INFO: 英语能力: good [] []
[2025-05-26 18:08:54] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:08:55] default.INFO: AI推荐学校请求参数: {"report_id":136} [] []
[2025-05-26 18:08:55] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 18:08:55] default.INFO: 梦想院校 [] []
[2025-05-26 18:08:55] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-05-26 18:08:55] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":138},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":160},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":99},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":172},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190}] [] []
[2025-05-26 18:08:56] default.INFO: 启动爬虫响应: {"task_id":"8b09eebf-93eb-44a9-98c9-cc7aa8f9514f","status":"running","message":"任务已创建，正在后台处理","total":6} [] []
[2025-05-26 18:08:56] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 18:08:56] default.INFO: 上下文长度: 3163 字符 [] []
[2025-05-26 18:08:56] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 18:08:56] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：144分\n数学（数学一）：110分\n专业课：120分\n总分：451分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：451分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线138分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线160分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线99分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线172分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线190分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线190分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 18:09:39] default.INFO: DeepSeek API 请求总时间: 43.577945947647秒 [] []
[2025-05-26 18:09:39] default.INFO: DeepSeek API 响应大小: 5299字节 [] []
[2025-05-26 18:09:39] default.INFO: DeepSeek API 响应: {"id":"406869a0-6ae8-4210-9312-53305f4fd12f","object":"chat.completion","created":1748254136,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学水利工程专业竞争难度适中，分数线为313分，学生预估分数高出138分，录取概率较大。该校水利工程专业在211院校中排名靠前，但竞争压力相对985院校较小。\",\n            \"suggest\": \"备考目标建议保持当前分数水平，重点关注专业课和数学的进一步提升，确保总分优势。\",\n            \"reason\": \"推荐原因：中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域偏好。该校水利工程专业实力较强，学生预估分数远超分数线，录取概率高。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学水利工程专业竞争难度较低，分数线为291分，学生预估分数高出160分，录取概率非常高。该校水利工程专业在安徽省内具有较高声誉。\",\n            \"suggest\": \"备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。\",\n            \"reason\": \"推荐原因：合肥工业大学位于安徽省，符合学生的目标区域偏好。该校水利工程专业分数线较低，学生预估分数远超分数线，录取概率极高。\"\n        },\n        {\n            \"school_name\": \"天津大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"天津大学水利工程专业竞争难度较高，分数线为352分，学生预估分数高出99分。该校为985院校，竞争压力较大，但学生分数优势明显。\",\n            \"suggest\": \"备考目标建议进一步提升数学和专业课分数，确保总分优势，同时关注复试准备。\",\n            \"reason\": \"推荐原因：天津大学是985院校，水利工程专业实力强劲，位于天津市，符合学生的目标区域偏好。学生预估分数远超分数线，录取概率较高。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学水利工程专业竞争难度较低，分数线为279分，学生预估分数高出172分，录取概率极高。该校水利工程专业在江苏省内具有一定影响力。\",\n            \"suggest\": \"备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。\",\n            \"reason\": \"推荐原因：江苏大学位于江苏省，符合学生的目标区域偏好。该校水利工程专业分数线较低，学生预估分数远超分数线，录取概率极高。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争难度较低，分数线为261分，学生预估分数高出190分，录取概率极高。该校水利工程专业在浙江省内具有一定特色。\",\n            \"suggest\": \"备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。\",\n            \"reason\": \"推荐原因：浙江海洋大学位于浙江省，符合学生的目标区域偏好。该校水利工程专业分数线极低，学生预估分数远超分数线，录取概率极高。\"\n        },\n        {\n            \"school_name\": \"上海海事大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"上海海事大学水利工程专业竞争难度较低，分数线为261分，学生预估分数高出190分，录取概率极高。该校水利工程专业在上海市内具有一定特色。\",\n            \"suggest\": \"备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。\",\n            \"reason\": \"推荐原因：上海海事大学位于上海市，符合学生的目标区域偏好。该校水利工程专业分数线极低，学生预估分数远超分数线，录取概率极高。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"综合性价比高：中国矿业大学是学生的梦想院校，水利工程专业实力较强，位于江苏省，符合学生的目标区域偏好。学生预估分数远超分数线，录取概率高，且该校为211院校，性价比极高。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":839,"completion_tokens":910,"total_tokens":1749,"prompt_tokens_details":{"cached_tokens":256},"prompt_cache_hit_tokens":256,"prompt_cache_miss_tokens":583},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 18:09:39] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 18:09:39] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学水利工程专业竞争难度适中，分数线为313分，学生预估分数高出138分，录取概率较大。该校水利工程专业在211院校中排名靠前，但竞争压力相对985院校较小。",
            "suggest": "备考目标建议保持当前分数水平，重点关注专业课和数学的进一步提升，确保总分优势。",
            "reason": "推荐原因：中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域偏好。该校水利工程专业实力较强，学生预估分数远超分数线，录取概率高。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学水利工程专业竞争难度较低，分数线为291分，学生预估分数高出160分，录取概率非常高。该校水利工程专业在安徽省内具有较高声誉。",
            "suggest": "备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。",
            "reason": "推荐原因：合肥工业大学位于安徽省，符合学生的目标区域偏好。该校水利工程专业分数线较低，学生预估分数远超分数线，录取概率极高。"
        },
        {
            "school_name": "天津大学",
            "major_name": "水利工程",
            "difficulty_analysis": "天津大学水利工程专业竞争难度较高，分数线为352分，学生预估分数高出99分。该校为985院校，竞争压力较大，但学生分数优势明显。",
            "suggest": "备考目标建议进一步提升数学和专业课分数，确保总分优势，同时关注复试准备。",
            "reason": "推荐原因：天津大学是985院校，水利工程专业实力强劲，位于天津市，符合学生的目标区域偏好。学生预估分数远超分数线，录取概率较高。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学水利工程专业竞争难度较低，分数线为279分，学生预估分数高出172分，录取概率极高。该校水利工程专业在江苏省内具有一定影响力。",
            "suggest": "备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。",
            "reason": "推荐原因：江苏大学位于江苏省，符合学生的目标区域偏好。该校水利工程专业分数线较低，学生预估分数远超分数线，录取概率极高。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争难度较低，分数线为261分，学生预估分数高出190分，录取概率极高。该校水利工程专业在浙江省内具有一定特色。",
            "suggest": "备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。",
            "reason": "推荐原因：浙江海洋大学位于浙江省，符合学生的目标区域偏好。该校水利工程专业分数线极低，学生预估分数远超分数线，录取概率极高。"
        },
        {
            "school_name": "上海海事大学",
            "major_name": "水利工程",
            "difficulty_analysis": "上海海事大学水利工程专业竞争难度较低，分数线为261分，学生预估分数高出190分，录取概率极高。该校水利工程专业在上海市内具有一定特色。",
            "suggest": "备考目标建议保持当前分数水平，重点关注专业课和英语的稳定发挥，确保总分优势。",
            "reason": "推荐原因：上海海事大学位于上海市，符合学生的目标区域偏好。该校水利工程专业分数线极低，学生预估分数远超分数线，录取概率极高。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "reason": "综合性价比高：中国矿业大学是学生的梦想院校，水利工程专业实力较强，位于江苏省，符合学生的目标区域偏好。学生预估分数远超分数线，录取概率高，且该校为211院校，性价比极高。"
        }
    ]
}
``` [] []
[2025-05-26 18:09:39] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 18:09:39] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 18:09:39] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 18:09:40] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 18:09:40] default.INFO: 找到学校数据: 天津大学, ID: 18, logo:  [] []
[2025-05-26 18:09:40] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 18:09:41] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 18:09:41] default.INFO: 找到学校数据: 上海海事大学, ID: 259, logo:  [] []
[2025-05-26 18:09:41] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 18:09:41] default.INFO: 开始保存推荐院校数据到数据库，reportId: 136, studentId: 3 [] []
[2025-05-26 18:09:41] default.INFO: 已删除报告ID 136 的旧推荐数据 [] []
[2025-05-26 18:09:42] default.INFO: 推荐院校数据保存完成，共插入 7 条记录 [] []
[2025-05-26 18:09:42] default.INFO: 学校已存在，跳过: 中国矿业大学 [] []
[2025-05-26 18:09:42] default.INFO: 最终学校列表数量: 6 [] []
[2025-05-26 18:09:42] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","天津大学","江苏大学","浙江海洋大学","上海海事大学"] [] []
[2025-05-26 18:09:42] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 18:11:23] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 18:53:35] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 18:54:19] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:54:19] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:54:19] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:54:19] default.INFO: 托福成绩:  [] []
[2025-05-26 18:54:19] default.INFO: 英语能力: good [] []
[2025-05-26 18:54:19] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:54:20] default.INFO: AI推荐学校请求参数: {"report_id":137} [] []
[2025-05-26 18:54:21] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 18:54:21] default.INFO: 梦想院校 [] []
[2025-05-26 18:54:21] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-05-26 18:54:21] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":138},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":160},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":99},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":172},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190}] [] []
[2025-05-26 18:54:21] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 18:54:21] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 18:54:21] default.INFO: 上下文长度: 3163 字符 [] []
[2025-05-26 18:54:21] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 18:54:21] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：144分\n数学（数学一）：110分\n专业课：120分\n总分：451分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：451分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线138分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线160分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线99分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线172分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线190分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线190分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 18:55:02] default.INFO: DeepSeek API 请求总时间: 41.399210929871秒 [] []
[2025-05-26 18:55:02] default.INFO: DeepSeek API 响应大小: 4839字节 [] []
[2025-05-26 18:55:02] default.INFO: DeepSeek API 响应: {"id":"0e95efdd-db3f-427d-a872-055cac5a9738","object":"chat.completion","created":1748256861,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学水利工程专业竞争较为激烈，作为211高校，其水利工程专业在全国有一定影响力。虽然分数线为313分，但实际录取分数可能更高，尤其是热门研究方向。\",\n            \"suggest\": \"备考时需重点关注专业课和数学，确保这两门科目分数稳定。同时，提前联系导师，了解研究方向。\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和省份偏好。学校水利工程专业实力较强，适合学生继续深造。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学水利工程专业竞争相对适中，分数线为291分，但实际录取分数可能略高。学校在安徽省内认可度较高。\",\n            \"suggest\": \"备考时需注重专业课和数学的复习，同时提升英语成绩以增加竞争力。\",\n            \"reason\": \"合肥工业大学是211高校，位于安徽省，符合学生的目标区域和省份偏好。学校水利工程专业实力不错，且竞争相对适中。\"\n        },\n        {\n            \"school_name\": \"天津大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"天津大学作为985高校，水利工程专业竞争非常激烈，分数线为352分，实际录取分数可能更高。学校在该领域有很强的科研实力。\",\n            \"suggest\": \"备考时需全面提升各科成绩，尤其是数学和专业课。建议提前联系导师，争取科研机会。\",\n            \"reason\": \"天津大学是985高校，水利工程专业全国领先，位于天津市，符合学生的目标区域偏好。虽然竞争激烈，但学生预估分数较高，有冲刺可能。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学水利工程专业竞争相对较小，分数线为279分，适合分数中等偏上的学生报考。学校在江苏省内有一定影响力。\",\n            \"suggest\": \"备考时需确保专业课和数学成绩稳定，同时提升英语和政治成绩以增加优势。\",\n            \"reason\": \"江苏大学位于江苏省，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争较小，分数线为261分，适合分数中等偏上的学生报考。学校在浙江省内有一定认可度。\",\n            \"suggest\": \"备考时需注重专业课和数学的复习，同时确保英语和政治成绩达标。\",\n            \"reason\": \"浙江海洋大学位于浙江省，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。\"\n        },\n        {\n            \"school_name\": \"上海海事大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"上海海事大学水利工程专业竞争较小，分数线为261分，适合分数中等偏上的学生报考。学校在上海市内有一定认可度。\",\n            \"suggest\": \"备考时需确保专业课和数学成绩稳定，同时提升英语和政治成绩以增加优势。\",\n            \"reason\": \"上海海事大学位于上海市，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和省份偏好。学校水利工程专业实力较强，竞争适中，综合性价比高。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":839,"completion_tokens":838,"total_tokens":1677,"prompt_tokens_details":{"cached_tokens":832},"prompt_cache_hit_tokens":832,"prompt_cache_miss_tokens":7},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 18:55:02] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 18:55:02] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学水利工程专业竞争较为激烈，作为211高校，其水利工程专业在全国有一定影响力。虽然分数线为313分，但实际录取分数可能更高，尤其是热门研究方向。",
            "suggest": "备考时需重点关注专业课和数学，确保这两门科目分数稳定。同时，提前联系导师，了解研究方向。",
            "reason": "中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和省份偏好。学校水利工程专业实力较强，适合学生继续深造。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学水利工程专业竞争相对适中，分数线为291分，但实际录取分数可能略高。学校在安徽省内认可度较高。",
            "suggest": "备考时需注重专业课和数学的复习，同时提升英语成绩以增加竞争力。",
            "reason": "合肥工业大学是211高校，位于安徽省，符合学生的目标区域和省份偏好。学校水利工程专业实力不错，且竞争相对适中。"
        },
        {
            "school_name": "天津大学",
            "major_name": "水利工程",
            "difficulty_analysis": "天津大学作为985高校，水利工程专业竞争非常激烈，分数线为352分，实际录取分数可能更高。学校在该领域有很强的科研实力。",
            "suggest": "备考时需全面提升各科成绩，尤其是数学和专业课。建议提前联系导师，争取科研机会。",
            "reason": "天津大学是985高校，水利工程专业全国领先，位于天津市，符合学生的目标区域偏好。虽然竞争激烈，但学生预估分数较高，有冲刺可能。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学水利工程专业竞争相对较小，分数线为279分，适合分数中等偏上的学生报考。学校在江苏省内有一定影响力。",
            "suggest": "备考时需确保专业课和数学成绩稳定，同时提升英语和政治成绩以增加优势。",
            "reason": "江苏大学位于江苏省，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争较小，分数线为261分，适合分数中等偏上的学生报考。学校在浙江省内有一定认可度。",
            "suggest": "备考时需注重专业课和数学的复习，同时确保英语和政治成绩达标。",
            "reason": "浙江海洋大学位于浙江省，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。"
        },
        {
            "school_name": "上海海事大学",
            "major_name": "水利工程",
            "difficulty_analysis": "上海海事大学水利工程专业竞争较小，分数线为261分，适合分数中等偏上的学生报考。学校在上海市内有一定认可度。",
            "suggest": "备考时需确保专业课和数学成绩稳定，同时提升英语和政治成绩以增加优势。",
            "reason": "上海海事大学位于上海市，符合学生的目标区域偏好。学校水利工程专业竞争较小，适合学生作为保底选择。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "reason": "中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和省份偏好。学校水利工程专业实力较强，竞争适中，综合性价比高。"
        }
    ]
}
``` [] []
[2025-05-26 18:55:02] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 18:55:02] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 18:55:02] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 18:55:03] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 18:55:03] default.INFO: 找到学校数据: 天津大学, ID: 18, logo:  [] []
[2025-05-26 18:55:03] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 18:55:04] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 18:55:04] default.INFO: 找到学校数据: 上海海事大学, ID: 259, logo:  [] []
[2025-05-26 18:55:04] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 18:55:04] default.INFO: 开始保存推荐院校数据到数据库，reportId: 137, studentId: 3 [] []
[2025-05-26 18:55:05] default.INFO: 已删除报告ID 137 的旧推荐数据 [] []
[2025-05-26 18:55:05] default.INFO: 推荐院校数据保存完成，共插入 7 条记录 [] []
[2025-05-26 18:55:05] default.INFO: 学校已存在，跳过: 中国矿业大学 [] []
[2025-05-26 18:55:05] default.INFO: 最终学校列表数量: 6 [] []
[2025-05-26 18:55:05] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","天津大学","江苏大学","浙江海洋大学","上海海事大学"] [] []
[2025-05-26 18:55:06] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 18:56:46] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 18:58:58] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 18:59:19] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 18:59:20] default.INFO: 四级成绩: 481 [] []
[2025-05-26 18:59:20] default.INFO: 六级成绩: 510 [] []
[2025-05-26 18:59:20] default.INFO: 托福成绩:  [] []
[2025-05-26 18:59:20] default.INFO: 英语能力: good [] []
[2025-05-26 18:59:20] default.INFO: 地区倾向: A区 [] []
[2025-05-26 18:59:21] default.INFO: AI推荐学校请求参数: {"report_id":138} [] []
[2025-05-26 18:59:21] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 18:59:21] default.INFO: 梦想院校 [] []
[2025-05-26 18:59:21] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-05-26 18:59:21] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":138},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":160},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":99},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":172},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190}] [] []
[2025-05-26 18:59:21] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 18:59:21] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 18:59:21] default.INFO: 上下文长度: 3163 字符 [] []
[2025-05-26 18:59:21] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 18:59:21] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：144分\n数学（数学一）：110分\n专业课：120分\n总分：451分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：451分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线138分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线160分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线99分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线172分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线190分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线190分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 19:00:05] default.INFO: DeepSeek API 请求总时间: 44.210880994797秒 [] []
[2025-05-26 19:00:06] default.INFO: DeepSeek API 响应大小: 5141字节 [] []
[2025-05-26 19:00:06] default.INFO: DeepSeek API 响应: {"id":"c0c649da-70ab-4d29-b484-a7dc20439f2e","object":"chat.completion","created":1748257161,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学水利工程专业竞争较为激烈，录取分数线较高，但学生预估总分451分远超313分的分数线，竞争力强。该校水利工程学科实力雄厚，科研资源丰富，适合有科研兴趣的学生。\",\n            \"suggest\": \"备考时需重点关注专业课和数学，确保高分。同时，提前联系导师，了解研究方向，增加录取机会。\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且水利工程专业实力强，预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学水利工程专业竞争相对适中，录取分数线为291分，学生预估总分高出160分，优势明显。该校水利工程学科在安徽省内具有较高声誉。\",\n            \"suggest\": \"备考时保持各科均衡，尤其是专业课和数学，确保高分。可提前了解导师研究方向，为面试做准备。\",\n            \"reason\": \"合肥工业大学是211院校，水利工程专业实力较强，且学生预估分数远超分数线，录取概率高。学校位于安徽省，符合学生的地域偏好。\"\n        },\n        {\n            \"school_name\": \"天津大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"天津大学水利工程专业竞争非常激烈，录取分数线为352分，学生预估总分高出99分。该校水利工程学科全国排名靠前，科研实力强。\",\n            \"suggest\": \"备考时需全面提升各科成绩，尤其是专业课和数学。建议提前联系导师，争取科研机会，增加录取筹码。\",\n            \"reason\": \"天津大学是985、211院校，水利工程专业全国领先，学生预估分数较高，录取机会大。学校位于天津市，符合学生的地域偏好。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学水利工程专业竞争相对较小，录取分数线为279分，学生预估总分高出172分。该校水利工程学科在江苏省内有一定影响力。\",\n            \"suggest\": \"备考时重点保持高分，尤其是专业课和数学。可提前了解导师研究方向，为面试做准备。\",\n            \"reason\": \"江苏大学水利工程专业实力不错，学生预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争较小，录取分数线为261分，学生预估总分高出190分。该校水利工程学科在海洋水利方面有特色。\",\n            \"suggest\": \"备考时保持各科高分，尤其是专业课和数学。可提前了解海洋水利方向，增加录取优势。\",\n            \"reason\": \"浙江海洋大学水利工程专业竞争较小，学生预估分数远超分数线，录取概率高。学校位于浙江省，符合学生的地域偏好。\"\n        },\n        {\n            \"school_name\": \"上海海事大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"上海海事大学水利工程专业竞争较小，录取分数线为261分，学生预估总分高出190分。该校水利工程学科在港口与航道工程方面有特色。\",\n            \"suggest\": \"备考时保持各科高分，尤其是专业课和数学。可提前了解港口与航道工程方向，增加录取优势。\",\n            \"reason\": \"上海海事大学水利工程专业竞争较小，学生预估分数远超分数线，录取概率高。学校位于上海市，符合学生的地域偏好。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，水利工程专业实力强，预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好，综合性价比高。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":839,"completion_tokens":898,"total_tokens":1737,"prompt_tokens_details":{"cached_tokens":832},"prompt_cache_hit_tokens":832,"prompt_cache_miss_tokens":7},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 19:00:06] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 19:00:06] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学水利工程专业竞争较为激烈，录取分数线较高，但学生预估总分451分远超313分的分数线，竞争力强。该校水利工程学科实力雄厚，科研资源丰富，适合有科研兴趣的学生。",
            "suggest": "备考时需重点关注专业课和数学，确保高分。同时，提前联系导师，了解研究方向，增加录取机会。",
            "reason": "中国矿业大学是学生的梦想院校，且水利工程专业实力强，预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学水利工程专业竞争相对适中，录取分数线为291分，学生预估总分高出160分，优势明显。该校水利工程学科在安徽省内具有较高声誉。",
            "suggest": "备考时保持各科均衡，尤其是专业课和数学，确保高分。可提前了解导师研究方向，为面试做准备。",
            "reason": "合肥工业大学是211院校，水利工程专业实力较强，且学生预估分数远超分数线，录取概率高。学校位于安徽省，符合学生的地域偏好。"
        },
        {
            "school_name": "天津大学",
            "major_name": "水利工程",
            "difficulty_analysis": "天津大学水利工程专业竞争非常激烈，录取分数线为352分，学生预估总分高出99分。该校水利工程学科全国排名靠前，科研实力强。",
            "suggest": "备考时需全面提升各科成绩，尤其是专业课和数学。建议提前联系导师，争取科研机会，增加录取筹码。",
            "reason": "天津大学是985、211院校，水利工程专业全国领先，学生预估分数较高，录取机会大。学校位于天津市，符合学生的地域偏好。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学水利工程专业竞争相对较小，录取分数线为279分，学生预估总分高出172分。该校水利工程学科在江苏省内有一定影响力。",
            "suggest": "备考时重点保持高分，尤其是专业课和数学。可提前了解导师研究方向，为面试做准备。",
            "reason": "江苏大学水利工程专业实力不错，学生预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争较小，录取分数线为261分，学生预估总分高出190分。该校水利工程学科在海洋水利方面有特色。",
            "suggest": "备考时保持各科高分，尤其是专业课和数学。可提前了解海洋水利方向，增加录取优势。",
            "reason": "浙江海洋大学水利工程专业竞争较小，学生预估分数远超分数线，录取概率高。学校位于浙江省，符合学生的地域偏好。"
        },
        {
            "school_name": "上海海事大学",
            "major_name": "水利工程",
            "difficulty_analysis": "上海海事大学水利工程专业竞争较小，录取分数线为261分，学生预估总分高出190分。该校水利工程学科在港口与航道工程方面有特色。",
            "suggest": "备考时保持各科高分，尤其是专业课和数学。可提前了解港口与航道工程方向，增加录取优势。",
            "reason": "上海海事大学水利工程专业竞争较小，学生预估分数远超分数线，录取概率高。学校位于上海市，符合学生的地域偏好。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "reason": "中国矿业大学是学生的梦想院校，水利工程专业实力强，预估分数远超分数线，录取概率高。学校位于江苏省，符合学生的地域偏好，综合性价比高。"
        }
    ]
}
``` [] []
[2025-05-26 19:00:06] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 19:00:06] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 19:00:06] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 19:00:06] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 19:00:07] default.INFO: 找到学校数据: 天津大学, ID: 18, logo:  [] []
[2025-05-26 19:00:07] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 19:00:07] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 19:00:08] default.INFO: 找到学校数据: 上海海事大学, ID: 259, logo:  [] []
[2025-05-26 19:00:08] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 19:00:08] default.INFO: 开始保存推荐院校数据到数据库，reportId: 138, studentId: 3 [] []
[2025-05-26 19:00:08] default.INFO: 已删除报告ID 138 的旧推荐数据 [] []
[2025-05-26 19:00:08] default.INFO: 推荐院校数据保存完成，共插入 7 条记录 [] []
[2025-05-26 19:00:09] default.INFO: 学校已存在，跳过: 中国矿业大学 [] []
[2025-05-26 19:00:09] default.INFO: 最终学校列表数量: 6 [] []
[2025-05-26 19:00:09] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","天津大学","江苏大学","浙江海洋大学","上海海事大学"] [] []
[2025-05-26 19:00:09] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 19:16:55] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e09","page":"1","limit":"10"} [] []
[2025-05-26 19:17:06] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-05-26 19:17:19] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"144","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"451","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 19:17:19] default.INFO: 四级成绩: 481 [] []
[2025-05-26 19:17:19] default.INFO: 六级成绩: 510 [] []
[2025-05-26 19:17:19] default.INFO: 托福成绩:  [] []
[2025-05-26 19:17:19] default.INFO: 英语能力: good [] []
[2025-05-26 19:17:19] default.INFO: 地区倾向: A区 [] []
[2025-05-26 19:17:20] default.INFO: AI推荐学校请求参数: {"report_id":139} [] []
[2025-05-26 19:17:21] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-05-26 19:17:21] default.INFO: 梦想院校 [] []
[2025-05-26 19:17:21] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-05-26 19:17:21] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":138},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":160},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":99},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":172},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":190}] [] []
[2025-05-26 19:17:21] default.INFO: 不需要启动爬虫 [] []
[2025-05-26 19:17:21] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 19:17:21] default.INFO: 上下文长度: 3163 字符 [] []
[2025-05-26 19:17:21] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 19:17:21] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：144分\n数学（数学一）：110分\n专业课：120分\n总分：451分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：451分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线138分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线160分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线99分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线172分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线190分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线190分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 19:18:01] default.INFO: DeepSeek API 请求总时间: 40.217175960541秒 [] []
[2025-05-26 19:18:01] default.INFO: DeepSeek API 响应大小: 4644字节 [] []
[2025-05-26 19:18:01] default.INFO: DeepSeek API 响应: {"id":"65181430-2b23-4e52-a754-f7d98709d582","object":"chat.completion","created":1748258241,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"中国矿业大学水利工程专业竞争较为激烈，作为211高校，其水利工程专业在国内享有较高声誉。虽然分数线为313分，但实际录取分数可能更高，竞争压力较大。\",\n            \"suggest\": \"备考时需重点关注专业课和数学，保持英语优势，争取总分超过分数线50分以上以提高录取概率。\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和院校层次要求。学校水利工程专业实力强，适合学生继续深造。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"合肥工业大学水利工程专业竞争相对适中，分数线为291分，但作为211高校，录取分数可能接近或略高于分数线。\",\n            \"suggest\": \"备考时需保持各科均衡发展，尤其是数学和专业课，争取总分超过分数线60分以上。\",\n            \"reason\": \"合肥工业大学位于安徽省，符合学生的目标省份要求，且水利工程专业实力较强，录取难度相对较低。\"\n        },\n        {\n            \"school_name\": \"天津大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"天津大学作为985高校，水利工程专业竞争非常激烈，分数线为352分，实际录取分数可能更高，对学生的综合素质要求较高。\",\n            \"suggest\": \"备考时需全面提升各科成绩，尤其是数学和专业课，争取总分超过分数线80分以上。\",\n            \"reason\": \"天津大学水利工程专业在国内排名靠前，位于天津市，符合学生的目标区域要求，适合有较强竞争力的学生。\"\n        },\n        {\n            \"school_name\": \"江苏大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"江苏大学水利工程专业竞争相对较小，分数线为279分，录取难度较低，适合成绩中上的学生。\",\n            \"suggest\": \"备考时需保持英语和专业课优势，数学争取达到100分以上，总分超过分数线70分以上。\",\n            \"reason\": \"江苏大学位于江苏省，水利工程专业实力不错，录取难度较低，适合学生作为保底选择。\"\n        },\n        {\n            \"school_name\": \"浙江海洋大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"浙江海洋大学水利工程专业竞争较小，分数线为261分，录取难度低，适合成绩中等的学生。\",\n            \"suggest\": \"备考时需保持英语优势，数学和专业课争取达到平均水平，总分超过分数线60分以上。\",\n            \"reason\": \"浙江海洋大学位于浙江省，水利工程专业录取难度低，适合学生作为保底院校。\"\n        },\n        {\n            \"school_name\": \"上海海事大学\",\n            \"major_name\": \"水利工程\",\n            \"difficulty_analysis\": \"上海海事大学水利工程专业竞争较小，分数线为261分，录取难度低，适合成绩中等的学生。\",\n            \"suggest\": \"备考时需保持英语优势，数学和专业课争取达到平均水平，总分超过分数线60分以上。\",\n            \"reason\": \"上海海事大学位于上海市，水利工程专业录取难度低，适合学生作为保底院校。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"中国矿业大学\",\n            \"major_name\": \"水利工程\",\n            \"reason\": \"中国矿业大学是学生的梦想院校，水利工程专业实力强，位于江苏省，符合学生的目标区域和院校层次要求。虽然竞争较为激烈，但学生的预估总分较高，录取概率较大。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":839,"completion_tokens":807,"total_tokens":1646,"prompt_tokens_details":{"cached_tokens":832},"prompt_cache_hit_tokens":832,"prompt_cache_miss_tokens":7},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 19:18:01] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 19:18:01] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "中国矿业大学水利工程专业竞争较为激烈，作为211高校，其水利工程专业在国内享有较高声誉。虽然分数线为313分，但实际录取分数可能更高，竞争压力较大。",
            "suggest": "备考时需重点关注专业课和数学，保持英语优势，争取总分超过分数线50分以上以提高录取概率。",
            "reason": "中国矿业大学是学生的梦想院校，且位于江苏省，符合学生的目标区域和院校层次要求。学校水利工程专业实力强，适合学生继续深造。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "水利工程",
            "difficulty_analysis": "合肥工业大学水利工程专业竞争相对适中，分数线为291分，但作为211高校，录取分数可能接近或略高于分数线。",
            "suggest": "备考时需保持各科均衡发展，尤其是数学和专业课，争取总分超过分数线60分以上。",
            "reason": "合肥工业大学位于安徽省，符合学生的目标省份要求，且水利工程专业实力较强，录取难度相对较低。"
        },
        {
            "school_name": "天津大学",
            "major_name": "水利工程",
            "difficulty_analysis": "天津大学作为985高校，水利工程专业竞争非常激烈，分数线为352分，实际录取分数可能更高，对学生的综合素质要求较高。",
            "suggest": "备考时需全面提升各科成绩，尤其是数学和专业课，争取总分超过分数线80分以上。",
            "reason": "天津大学水利工程专业在国内排名靠前，位于天津市，符合学生的目标区域要求，适合有较强竞争力的学生。"
        },
        {
            "school_name": "江苏大学",
            "major_name": "水利工程",
            "difficulty_analysis": "江苏大学水利工程专业竞争相对较小，分数线为279分，录取难度较低，适合成绩中上的学生。",
            "suggest": "备考时需保持英语和专业课优势，数学争取达到100分以上，总分超过分数线70分以上。",
            "reason": "江苏大学位于江苏省，水利工程专业实力不错，录取难度较低，适合学生作为保底选择。"
        },
        {
            "school_name": "浙江海洋大学",
            "major_name": "水利工程",
            "difficulty_analysis": "浙江海洋大学水利工程专业竞争较小，分数线为261分，录取难度低，适合成绩中等的学生。",
            "suggest": "备考时需保持英语优势，数学和专业课争取达到平均水平，总分超过分数线60分以上。",
            "reason": "浙江海洋大学位于浙江省，水利工程专业录取难度低，适合学生作为保底院校。"
        },
        {
            "school_name": "上海海事大学",
            "major_name": "水利工程",
            "difficulty_analysis": "上海海事大学水利工程专业竞争较小，分数线为261分，录取难度低，适合成绩中等的学生。",
            "suggest": "备考时需保持英语优势，数学和专业课争取达到平均水平，总分超过分数线60分以上。",
            "reason": "上海海事大学位于上海市，水利工程专业录取难度低，适合学生作为保底院校。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "中国矿业大学",
            "major_name": "水利工程",
            "reason": "中国矿业大学是学生的梦想院校，水利工程专业实力强，位于江苏省，符合学生的目标区域和院校层次要求。虽然竞争较为激烈，但学生的预估总分较高，录取概率较大。"
        }
    ]
}
``` [] []
[2025-05-26 19:18:01] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 19:18:01] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 19:18:01] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 19:18:02] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 19:18:02] default.INFO: 找到学校数据: 天津大学, ID: 18, logo:  [] []
[2025-05-26 19:18:03] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-05-26 19:18:03] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-05-26 19:18:03] default.INFO: 找到学校数据: 上海海事大学, ID: 259, logo:  [] []
[2025-05-26 19:18:04] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-05-26 19:18:04] default.INFO: 开始保存推荐院校数据到数据库，reportId: 139, studentId: 3 [] []
[2025-05-26 19:18:04] default.INFO: 已删除报告ID 139 的旧推荐数据 [] []
[2025-05-26 19:18:04] default.INFO: 推荐院校数据保存完成，共插入 7 条记录 [] []
[2025-05-26 19:18:04] default.INFO: 学校已存在，跳过: 中国矿业大学 [] []
[2025-05-26 19:18:04] default.INFO: 最终学校列表数量: 6 [] []
[2025-05-26 19:18:04] default.INFO: 最终学校列表名称: ["中国矿业大学","合肥工业大学","天津大学","江苏大学","浙江海洋大学","上海海事大学"] [] []
[2025-05-26 19:18:05] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-05-26 19:24:36] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-05-26 19:26:00] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊","sex":"2","phone":"19855196869","undergraduateSchool":"196","undergraduateSchoolName":"安徽师范大学","undergraduateMajor":"11051","undergraduateMajorName":"计算机科学与技术","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,江苏省,安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"110","totalScore":"377","personalNeeds":"没有","weakModules":""} [] []
[2025-05-26 19:26:00] default.INFO: 四级成绩: 481 [] []
[2025-05-26 19:26:00] default.INFO: 六级成绩: 510 [] []
[2025-05-26 19:26:00] default.INFO: 托福成绩:  [] []
[2025-05-26 19:26:00] default.INFO: 英语能力: good [] []
[2025-05-26 19:26:00] default.INFO: 地区倾向: A区 [] []
[2025-05-26 19:26:01] default.INFO: AI推荐学校请求参数: {"report_id":140} [] []
[2025-05-26 19:26:01] default.INFO: 获取数据库中匹配的院校["18764","18774","26440","26463","18765","18775","18766","18776","26442","26465","18767","18777","18768","18778","26443","26466","26444","26467","26446","26469"] [] []
[2025-05-26 19:26:01] default.INFO: 梦想院校 [] []
[2025-05-26 19:26:01] default.INFO: 梦想院校{"id":"18761","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0812","second_level_name":"(081200)\u8ba1\u7b97\u673a\u79d1\u5b66\u4e0e\u6280\u672f","school_name":"\u5357\u4eac\u5927\u5b66","major_code":"081200","major_name":"081200\u8ba1\u7b97\u673a\u79d1\u5b66\u4e0e\u6280\u672f","area":"\u6c5f\u82cf\u7701","school_type":"985,211,\u53cc\u4e00\u6d41","college":"\u4eba\u5de5\u667a\u80fd\u5b66\u9662","study_type":"\u5168\u65e5\u5236","warning_level":"1:1","ranking":"9:262","admission":"\u603b\u5f55:14  \u4e00\u5fd7\u613f:14   \u8c03\u5242:0  \u5fc5\u8fbe\u5206:381","course_suggestion":"\u653f:73  \u96be   \u82f1:79  \u96be   \u4e13\u4e00:116  \u96be   \u4e13\u4e8c:116  \u7b80","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=XFAHfHGO1NgnkYASNvmVhlqF5zVe6N","detail_url_status":0,"created_at":"2025-04-29 23:50:38","must_reach_score":381,"crawl_start_time":null,"crawl_end_time":null} [] []
[2025-05-26 19:26:01] default.INFO: 学校列表: [{"school_id":"18764","school_name":"南京理工大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":344,"score_diff":33},{"school_id":"26440","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":333,"score_diff":44},{"school_id":"18765","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":330,"score_diff":47},{"school_id":"18766","school_name":"江南大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":326,"score_diff":51},{"school_id":"26442","school_name":"安徽大学","province":"安徽省","area":"安徽省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":324,"score_diff":53},{"school_id":"18767","school_name":"河海大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"211,双一流","must_reach_score":320,"score_diff":57},{"school_id":"18761","school_name":"南京大学","province":"江苏省","area":"江苏省","major_name":"081200计算机科学与技术","school_type":"985,211,双一流","must_reach_score":381,"score_diff":-4}] [] []
[2025-05-26 19:26:01] default.INFO: 启动爬虫响应: {"task_id":"e8997153-fac2-4850-b5fb-b4be5e16f370","status":"running","message":"任务已创建，正在后台处理","total":7} [] []
[2025-05-26 19:26:01] default.INFO: 开始调用 DeepSeek API [] []
[2025-05-26 19:26:01] default.INFO: 上下文长度: 3255 字符 [] []
[2025-05-26 19:26:02] default.INFO: DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-05-26 19:26:02] default.INFO: DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。\n                    谁推荐的院校只能从我给你的列表中做筛选\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"},{"role":"user","content":"学生基本信息：\n姓名：张珊\n性别：女\n本科院校：安徽师范大学\n本科专业：计算机科学与技术\n目标专业：(081200)计算机科学与技术（专业代码：081200）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：144分\n数学（数学一）：110分\n专业课：110分\n总分：377分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,江苏省,安徽省\n院校层次：211\n梦想院校：南京大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081200\n- 预估总分：377分\n- 目标区域：A区\n- 目标省份：天津市,江苏省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 南京理工大学（江苏省）- 081200计算机科学与技术，分数线：344分，高出分数线33分\n2. 合肥工业大学（安徽省）- 081200计算机科学与技术，分数线：333分，高出分数线44分\n3. 苏州大学（江苏省）- 081200计算机科学与技术，分数线：330分，高出分数线47分\n4. 江南大学（江苏省）- 081200计算机科学与技术，分数线：326分，高出分数线51分\n5. 安徽大学（安徽省）- 081200计算机科学与技术，分数线：324分，高出分数线53分\n6. 河海大学（江苏省）- 081200计算机科学与技术，分数线：320分，高出分数线57分\n\n【985,211,双一流类院校】\n1. 南京大学（江苏省）- 081200计算机科学与技术，分数线：381分，低于分数线4分\n\n【按省份分类】\n江苏省：南京理工大学、苏州大学、江南大学、河海大学、南京大学\n安徽省：合肥工业大学、安徽大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。为学生推荐10所不重复的最适合的学校，并以严格的格式返回结果。\n                    对每所学校，请提供竞争难度分析（不少于40字）和备考目标建议（不少于40字），以及推荐原因（不少于100字）。\n                    最后要求提供一所推荐综合性价比高的院校\n                    返回的结果以json格式返回,要求全部中文回复 格式如下：\n                   {\n                       'recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'difficulty_analysis': '竞争难度分析',\n                                'suggest': '备考目标建议',\n                                'reason': '推荐原因'\n                                }\n                        ],\n                        'high_recommend_list':[\n                                {\n                                'school_name': '学校名称',\n                                'major_name': '专业名称',\n                                'reason': '推荐原因'\n                                }\n                        ]\n                    }"}],"stream":false,"temperature":0.7,"max_tokens":4000} [] []
[2025-05-26 19:26:57] default.INFO: DeepSeek API 请求总时间: 55.762912034988秒 [] []
[2025-05-26 19:26:57] default.INFO: DeepSeek API 响应大小: 6394字节 [] []
[2025-05-26 19:26:57] default.INFO: DeepSeek API 响应: {"id":"0bf0f5d8-fe2f-44f5-900b-e156e5de8c1e","object":"chat.completion","created":1748258762,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n    \"recommend_list\": [\n        {\n            \"school_name\": \"南京理工大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，高出学生预估总分33分。该校计算机学科实力强劲，尤其在人工智能和网络安全方向有显著优势，报考人数较多，竞争压力较大。\",\n            \"suggest\": \"备考时需重点提升数学和专业课成绩，建议数学一目标提升至120分以上，专业课目标提升至120分以上，同时保持英语和政治的稳定发挥。\",\n            \"reason\": \"南京理工大学是江苏省内知名的211高校，计算机学科实力强，且地理位置优越，就业前景良好。学生预估总分377分，高出分数线33分，录取概率较大。\"\n        },\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"合肥工业大学计算机科学与技术专业分数线为333分，高出学生预估总分44分。该校计算机学科在安徽省内名列前茅，报考人数较多，但竞争压力相对南京理工大学略低。\",\n            \"suggest\": \"备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至115分以上，专业课目标提升至115分以上，同时英语和政治保持现有水平。\",\n            \"reason\": \"合肥工业大学是安徽省内顶尖的211高校，计算机学科实力强，且录取分数线相对较低，学生预估总分高出分数线44分，录取概率很高。\"\n        },\n        {\n            \"school_name\": \"苏州大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"苏州大学计算机科学与技术专业分数线为330分，高出学生预估总分47分。该校计算机学科发展迅速，尤其在软件工程和大数据方向有较强实力，报考人数逐年增加。\",\n            \"suggest\": \"备考时需重点提升专业课成绩，建议专业课目标提升至120分以上，同时保持数学和英语的稳定发挥，政治可适当冲刺80分。\",\n            \"reason\": \"苏州大学是江苏省内知名的211高校，计算机学科发展迅速，且地理位置优越，学生预估总分高出分数线47分，录取概率较大。\"\n        },\n        {\n            \"school_name\": \"江南大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"江南大学计算机科学与技术专业分数线为326分，高出学生预估总分51分。该校计算机学科在物联网和智能计算方向有特色，竞争压力相对较小。\",\n            \"suggest\": \"备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至110分以上，专业课目标提升至115分以上，英语和政治保持现有水平。\",\n            \"reason\": \"江南大学是江苏省内211高校，计算机学科特色鲜明，录取分数线较低，学生预估总分高出分数线51分，录取概率非常高。\"\n        },\n        {\n            \"school_name\": \"安徽大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"安徽大学计算机科学与技术专业分数线为324分，高出学生预估总分53分。该校计算机学科在安徽省内有一定影响力，报考人数较多，但竞争压力相对较小。\",\n            \"suggest\": \"备考时需重点提升数学和专业课成绩，建议数学一目标提升至115分以上，专业课目标提升至115分以上，英语和政治保持现有水平。\",\n            \"reason\": \"安徽大学是安徽省内211高校，计算机学科实力较强，且录取分数线较低，学生预估总分高出分数线53分，录取概率极高。\"\n        },\n        {\n            \"school_name\": \"河海大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"河海大学计算机科学与技术专业分数线为320分，高出学生预估总分57分。该校计算机学科在水资源信息化方向有特色，竞争压力相对较小。\",\n            \"suggest\": \"备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至110分以上，专业课目标提升至110分以上，英语和政治保持现有水平。\",\n            \"reason\": \"河海大学是江苏省内211高校，计算机学科特色鲜明，录取分数线较低，学生预估总分高出分数线57分，录取概率极高。\"\n        },\n        {\n            \"school_name\": \"南京大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"difficulty_analysis\": \"南京大学计算机科学与技术专业分数线为381分，低于学生预估总分4分。该校计算机学科实力顶尖，竞争非常激烈，报考人数众多，录取难度极大。\",\n            \"suggest\": \"备考时需全面提升各科成绩，建议数学一目标提升至130分以上，专业课目标提升至130分以上，英语和政治保持现有水平。\",\n            \"reason\": \"南京大学是学生梦想院校，计算机学科实力全国顶尖，虽然预估总分略低于分数线，但仍有冲刺可能，适合作为冲刺目标。\"\n        }\n    ],\n    \"high_recommend_list\": [\n        {\n            \"school_name\": \"合肥工业大学\",\n            \"major_name\": \"计算机科学与技术\",\n            \"reason\": \"合肥工业大学是安徽省内顶尖的211高校，计算机学科实力强，录取分数线较低，学生预估总分高出分数线44分，录取概率很高，且性价比极高。\"\n        }\n    ]\n}\n```"},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":849,"completion_tokens":1133,"total_tokens":1982,"prompt_tokens_details":{"cached_tokens":192},"prompt_cache_hit_tokens":192,"prompt_cache_miss_tokens":657},"system_fingerprint":"fp_8802369eaa_prod0425fp8"} [] []
[2025-05-26 19:26:57] default.INFO: DeepSeek API 响应数据结构: ["id","object","created","model","choices","usage","system_fingerprint"] [] []
[2025-05-26 19:26:57] default.INFO: DeepSeek API 响应内容: ```json
{
    "recommend_list": [
        {
            "school_name": "南京理工大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，高出学生预估总分33分。该校计算机学科实力强劲，尤其在人工智能和网络安全方向有显著优势，报考人数较多，竞争压力较大。",
            "suggest": "备考时需重点提升数学和专业课成绩，建议数学一目标提升至120分以上，专业课目标提升至120分以上，同时保持英语和政治的稳定发挥。",
            "reason": "南京理工大学是江苏省内知名的211高校，计算机学科实力强，且地理位置优越，就业前景良好。学生预估总分377分，高出分数线33分，录取概率较大。"
        },
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "合肥工业大学计算机科学与技术专业分数线为333分，高出学生预估总分44分。该校计算机学科在安徽省内名列前茅，报考人数较多，但竞争压力相对南京理工大学略低。",
            "suggest": "备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至115分以上，专业课目标提升至115分以上，同时英语和政治保持现有水平。",
            "reason": "合肥工业大学是安徽省内顶尖的211高校，计算机学科实力强，且录取分数线相对较低，学生预估总分高出分数线44分，录取概率很高。"
        },
        {
            "school_name": "苏州大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "苏州大学计算机科学与技术专业分数线为330分，高出学生预估总分47分。该校计算机学科发展迅速，尤其在软件工程和大数据方向有较强实力，报考人数逐年增加。",
            "suggest": "备考时需重点提升专业课成绩，建议专业课目标提升至120分以上，同时保持数学和英语的稳定发挥，政治可适当冲刺80分。",
            "reason": "苏州大学是江苏省内知名的211高校，计算机学科发展迅速，且地理位置优越，学生预估总分高出分数线47分，录取概率较大。"
        },
        {
            "school_name": "江南大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "江南大学计算机科学与技术专业分数线为326分，高出学生预估总分51分。该校计算机学科在物联网和智能计算方向有特色，竞争压力相对较小。",
            "suggest": "备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至110分以上，专业课目标提升至115分以上，英语和政治保持现有水平。",
            "reason": "江南大学是江苏省内211高校，计算机学科特色鲜明，录取分数线较低，学生预估总分高出分数线51分，录取概率非常高。"
        },
        {
            "school_name": "安徽大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "安徽大学计算机科学与技术专业分数线为324分，高出学生预估总分53分。该校计算机学科在安徽省内有一定影响力，报考人数较多，但竞争压力相对较小。",
            "suggest": "备考时需重点提升数学和专业课成绩，建议数学一目标提升至115分以上，专业课目标提升至115分以上，英语和政治保持现有水平。",
            "reason": "安徽大学是安徽省内211高校，计算机学科实力较强，且录取分数线较低，学生预估总分高出分数线53分，录取概率极高。"
        },
        {
            "school_name": "河海大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "河海大学计算机科学与技术专业分数线为320分，高出学生预估总分57分。该校计算机学科在水资源信息化方向有特色，竞争压力相对较小。",
            "suggest": "备考时需保持数学和专业课的稳定发挥，建议数学一目标提升至110分以上，专业课目标提升至110分以上，英语和政治保持现有水平。",
            "reason": "河海大学是江苏省内211高校，计算机学科特色鲜明，录取分数线较低，学生预估总分高出分数线57分，录取概率极高。"
        },
        {
            "school_name": "南京大学",
            "major_name": "计算机科学与技术",
            "difficulty_analysis": "南京大学计算机科学与技术专业分数线为381分，低于学生预估总分4分。该校计算机学科实力顶尖，竞争非常激烈，报考人数众多，录取难度极大。",
            "suggest": "备考时需全面提升各科成绩，建议数学一目标提升至130分以上，专业课目标提升至130分以上，英语和政治保持现有水平。",
            "reason": "南京大学是学生梦想院校，计算机学科实力全国顶尖，虽然预估总分略低于分数线，但仍有冲刺可能，适合作为冲刺目标。"
        }
    ],
    "high_recommend_list": [
        {
            "school_name": "合肥工业大学",
            "major_name": "计算机科学与技术",
            "reason": "合肥工业大学是安徽省内顶尖的211高校，计算机学科实力强，录取分数线较低，学生预估总分高出分数线44分，录取概率很高，且性价比极高。"
        }
    ]
}
``` [] []
[2025-05-26 19:26:57] default.INFO: 返回完整的大模型响应内容 [] []
[2025-05-26 19:26:57] default.INFO: 成功获取 DeepSeek API 推荐结果 [] []
[2025-05-26 19:26:58] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-05-26 19:26:58] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 19:26:59] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-05-26 19:26:59] default.INFO: 找到学校数据: 江南大学, ID: 110, logo:  [] []
[2025-05-26 19:26:59] default.INFO: 找到学校数据: 安徽大学, ID: 167, logo:  [] []
[2025-05-26 19:27:00] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-05-26 19:27:00] default.INFO: 找到学校数据: 南京大学, ID: 10, logo: yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/10.jpeg [] []
[2025-05-26 19:27:00] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-05-26 19:27:00] default.INFO: 开始保存推荐院校数据到数据库，reportId: 140, studentId: 2 [] []
[2025-05-26 19:27:00] default.INFO: 已删除报告ID 140 的旧推荐数据 [] []
[2025-05-26 19:27:00] default.INFO: 推荐院校数据保存完成，共插入 8 条记录 [] []
[2025-05-26 19:27:01] default.INFO: 学校已存在，跳过: 合肥工业大学 [] []
[2025-05-26 19:27:01] default.INFO: 最终学校列表数量: 7 [] []
[2025-05-26 19:27:01] default.INFO: 最终学校列表名称: ["南京理工大学","合肥工业大学","苏州大学","江南大学","安徽大学","河海大学","南京大学"] [] []
[2025-05-26 19:27:01] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-05-26 19:34:28] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 19:34:32] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-05-26 19:34:38] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
