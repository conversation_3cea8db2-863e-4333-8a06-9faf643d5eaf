做一下修改：
    当点击 toggleAIOverlay 请求 /api/student/toggle-ai-overlay
    后 获取的 返回的数据{"code":0,"msg":"更新成功","data":{"id":1,"report_id":3}}
    再次请求一个后端的接口 
    该接口的主要 逻辑是
    根据学生的目标专业，预估分数 所选区域， 省份信息。搜搜符合院校的20所学校

    整合该数据 构成上下文 
    再根据学生的 目标区域 目标
    最多20所学校 
    以此为 上下文调用 大模型的接口 让大模型推荐学校

    基于以上内容， 和已经实现的代码逻辑 代码在app/controller/remoteController.php 的 aiRemote方法中
    首先给我生成细化任务列表， 把细化的任务列表写入当前工作目录的task.txt

根据 大模型返回的推荐学校(包含 院校 一级专业， 二级专业) 首先
获取院校的 school_id 调用 spider.yanqukaoyan.com/api/crawl_school_info 接口 post 方式 参数 是json 
{
    "ids":[2001,2002,2003]
} 
获取院校的详细信息， 

获取院校 总览 该部分包含院校的基本信息
序号
院校名称
所在地区
所在省市
学院
专业
专业代码
最低分
最低分分差



其中院校的  访问大模型的 python代码　如下：
import requests

url = "https://api.siliconflow.cn/v1/chat/completions"
token = "sk-ywgzgpcdwcdjavznbalccqyhrvibrijbrtbgoidbveyoanvz"
payload = {
    "model": "Qwen/QwQ-32B",
    "messages": [
        {
            "role": "user",
            "content": "What opportunities and challenges will the Chinese large model industry face in 2025?"
        }
    ],
    "stream": False,
    "max_tokens": 512,
    "enable_thinking": False,
    "thinking_budget": 4096,
    "min_p": 0.05,
    "stop": None,
    "temperature": 0.7,
    "top_p": 0.7,
    "top_k": 50,
    "frequency_penalty": 0.5,
    "n": 1,
    "response_format": {"type": "text"},
    "tools": [
        {
            "type": "function",
            "function": {
                "description": "<string>",
                "name": "<string>",
                "parameters": {},
                "strict": False
            }
        }
    ]
}
headers = {
    "Authorization": f"Bearer <{token}>",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)
其中 对于components/content.vue echarts 图表显示 是根据用户报考的专业 获取对应的一级专业
 最近三年的信息 数据表是ba_national_line
 对于 ai 大模型 返回的





修改后端的逻辑
1，创建一份 择校报告表。
2， 要求有 学生id ，学生的目标专业，梦校，地区倾向，省份选择，院校层次，专业课制定参考书， 
4，各科的预估分数， 个性化需求和 薄弱模块, 生成的时间
5，改表的记录就是 在点击toggleAIOverlay 向后端 发送的信息


report-list.vue 操作中加上 查看按钮。 功能 就是根据 report_id 获取 
对应报告的 数据
渲染改数据。效果就是 generate的预览效果 
后端 generate.vue 从后端返回的数据是  返回的数据
在 data.json中
其中涉及的表： ba_student， ba_student_detail


复制的部分，单独封装成组件 
点击预览报告的时候。弹窗中展示此组件
该组件 放在 src/report/components中
渲染该组件传递三个属性  reportForm学员基本信息部分