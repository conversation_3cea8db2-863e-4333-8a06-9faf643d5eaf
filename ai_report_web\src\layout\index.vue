<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo-container">
          <img
            v-if="!isCollapse"
            src="@/assets/images/left-slide-logo.png"
            alt="Logo"
            class="sidebar-logo"
          />
          <img
            v-else
            src="@/assets/images/logo.png"
            alt="Logo"
            class="sidebar-logo"
          />
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            text-color="#333"
            active-text-color="#16b788"
            :router="true"
            :collapse="isCollapse"
            class="custom-menu"
          >
            <el-sub-menu index="1">
              <template #title>
                <el-icon>
                  <component is="DataLine" />
                </el-icon>
                <span style="margin-left: 12px">AI择校报告</span>
              </template>
              <el-menu-item index="/report/generate" class="custom-menu-item"
                >生成报告</el-menu-item
              >
              <el-menu-item index="/report/list" class="custom-menu-item"
                >报告列表</el-menu-item
              >
            </el-sub-menu>

            <el-sub-menu index="2">
              <template #title>
                <el-icon class="menu-icon"> </el-icon>
                <span style="margin-left: 12px">学员数据</span>
              </template>
              <el-menu-item index="/student/list" class="custom-menu-item"
                >学员列表</el-menu-item
              >
              <el-menu-item index="/student/tags" class="custom-menu-item"
                >标签管理</el-menu-item
              >
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      <el-container class="right-container">
        <!-- 顶部导航栏 -->
        <div class="header-section">
          <div class="header-left">
            <div class="logo">
              <span class="logo-text">AI择校报告</span>
            </div>
            <div
              v-if="route.path === '/report/generate'"
              class="search-container"
            >
              <div class="search-box">
                <el-autocomplete
                  v-model="searchQuery"
                  :fetch-suggestions="querySearch"
                  placeholder="请输入学员姓名"
                  class="search-input"
                  :trigger-on-focus="true"
                  @select="handleSelectStudent"
                  :loading="searchLoading"
                  popper-class="student-suggestions"
                  value-key="name"
                  clearable
                  :debounce="300"
                >
                  <template #suffix>
                    <el-icon class="search-icon" @click="handleSearch">
                      <Search />
                    </el-icon>
                  </template>
                  <template #default="{ item }">
                    <div class="student-suggestion-item">
                      <div class="student-name">{{ item.name }}</div>
                      <div class="student-info">
                        <span>{{
                          item.undergraduateSchoolName || "未知院校"
                        }}</span>
                        <span>{{
                          item.undergraduateMajorName || "未知专业"
                        }}</span>
                      </div>
                    </div>
                  </template>
                </el-autocomplete>
              </div>
              <el-button
                type="primary"
                class="create-btn"
                @click="handleAddStudent"
                >+ 添加学员</el-button
              >
            </div>
          </div>
          <div class="action-buttons">
            <div class="user-info">
              <el-avatar size="small" class="user-avatar"></el-avatar>
              <span class="welcome-text"
                >欢迎您 {{ userStore.nickname }}！</span
              >
              <span class="logout-text" @click="handleLogout">[退出登录]</span>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 学员表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加学员"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      class="student-dialog"
    >
      <div class="dialog-container">
        <div class="dialog-sidebar">
          <div
            v-for="(section, index) in formSections"
            :key="index"
            class="sidebar-item"
            :class="{
              active: activeSection == index,
            }"
            @click="scrollToSection(index)"
          >
            <div class="sidebar-item-number">{{ index + 1 }}</div>
            <div class="sidebar-item-text">{{ section }}</div>
          </div>
        </div>
        <div class="dialog-content" ref="formContent">
          <el-form
            :model="studentForm"
            label-width="100px"
            :rules="formRules"
            ref="studentFormRef"
            class="student-form"
          >
            <student-basic
              :student-form="studentForm"
              :dialog-type="dialogType"
              :school-options="schoolOptions"
              :major-options="majorOptions"
              :target-major-options="targetMajorOptions"
              :tag-options="tagOptions"
              :school-search-loading="schoolSearchLoading"
              :major-search-loading="majorSearchLoading"
              :target-major-search-loading="targetMajorSearchLoading"
              @school-change="handleSchoolChange"
              @search-school="remoteSearchSchool"
              @search-major="remoteSearchMajor"
              @search-target-major="remoteSearchTargetMajor"
            />

            <!-- 这里可以添加其他组件，如成绩情况、英语基础等 -->
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitLoading"
            class="action-button"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { useUserStore } from "../store/modules/user";
import { ElMessageBox, ElMessage } from "element-plus";
import StudentBasic from "@/views/student/components/StudentBasic.vue";
import TeacherSelector from "@/components/TeacherSelector.vue";
import { addStudent, getStudentList, getStudentDetail } from "@/api/student";
import { searchSchool, searchMajor, searchTargetMajor } from "@/api/school";
import { getAllTags } from "@/api/tag";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
// 搜索查询
const searchQuery = ref("");
const searchLoading = ref(false);
const searchTimeout = ref(null);
const searchCache = {};

// 搜索学员
const searchStudents = (query, callback) => {
  // 确保callback是函数
  if (typeof callback !== "function") {
    console.error("callback不是函数");
    return;
  }

  if (!query) {
    callback([]);
    return;
  }

  console.log("开始搜索学员:", query);

  // 检查缓存中是否已有该查询结果
  if (searchCache[query]) {
    console.log("使用缓存结果:", searchCache[query]);
    callback(searchCache[query]);
    return;
  }

  searchLoading.value = true;

  // 延迟300ms，避免频繁请求
  clearTimeout(searchTimeout.value);
  searchTimeout.value = setTimeout(() => {
    // 实际API调用
    getStudentList({ name: query, page: 1, limit: 10 })
      .then((res) => {
        console.log("搜索学员结果:", res);
        console.log(
          "搜索学员结果数据类型:",
          typeof res.data,
          Array.isArray(res.data)
        );
        console.log("搜索学员结果数据:", JSON.stringify(res.data, null, 2));

        if (res.code === 0 && res.data) {
          // 转换为下拉菜单需要的格式
          // 检查返回的数据结构，支持两种格式：直接数组或包含list属性的对象
          const studentList = Array.isArray(res.data)
            ? res.data
            : res.data.list || [];

          console.log("处理后的学员列表:", studentList);

          const students = studentList.map((student) => ({
            value: student.name, // 必须设置value属性，这是el-autocomplete需要的
            id: student.id,
            name: student.name,
            undergraduateSchoolName:
              student.undergraduateSchoolName || "未知院校",
            undergraduateMajorName:
              student.undergraduateMajorName || "未知专业",
            targetMajorName: student.targetMajorName,
          }));

          console.log("处理后的搜索结果:", students);

          // 添加到缓存
          searchCache[query] = students;
          callback(students);
        } else {
          // 如果API返回错误或没有数据，返回空数组
          console.log("API返回错误或没有数据，返回空结果");
          callback([]);
        }
      })
      .catch((err) => {
        console.error("搜索学员失败", err);
        callback([]);
      })
      .finally(() => {
        searchLoading.value = false;
      });
  }, 300);
};

// 查询搜索建议
const querySearch = (queryString, cb) => {
  console.log("查询搜索建议:", queryString);
  // 如果查询字符串为空，返回空数组
  if (!queryString) {
    cb([]);
    return;
  }
  // 调用搜索学员函数获取建议
  searchStudents(queryString, cb);
};

// 处理搜索按钮点击
const handleSearch = () => {
  console.log("搜索按钮点击:", searchQuery.value);
  if (searchQuery.value) {
    // 如果有查询内容，则执行搜索
    searchStudents(searchQuery.value, (results) => {
      console.log("搜索结果:", results);
      // 如果只有一个结果，直接选择
      if (results.length === 1) {
        handleSelectStudent(results[0]);
      }
    });
  }
};

// 处理选择学员
const handleSelectStudent = (student) => {
  console.log("选择学员:", student);
  if (student && student.id) {
    // 如果当前在报告生成页面，从后端获取学员详细信息并保存
    if (route.path === "/report/generate") {
      // 显示加载中
      ElMessage.info("正在加载学员信息...");

      // 获取学员详细信息
      getStudentDetail(student.id)
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 将学员信息存储到localStorage，以便报告生成页面使用
            localStorage.setItem("selectedStudent", JSON.stringify(res.data));

            // 刷新当前页面以加载学员信息
            ElMessage.success("学员信息加载成功，正在填充表单...");

            // 如果已经在报告生成页面，则通知页面刷新数据
            // 使用自定义事件通知
            const event = new CustomEvent("student-selected", {
              detail: res.data,
            });
            window.dispatchEvent(event);
          } else {
            ElMessage.error(res.msg || "获取学员信息失败");
            router.push(`/student/detail/${student.id}`);
          }
        })
        .catch((err) => {
          console.error("获取学员详情失败", err);
          ElMessage.error("获取学员信息失败，跳转到详情页");
          router.push(`/student/detail/${student.id}`);
        });
    } else {
      // 不在报告生成页面，直接跳转到学员详情页
      router.push(`/student/detail/${student.id}`);
    }
  }
};

// 侧边栏折叠状态
const isCollapse = ref(false);

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 当前活动菜单
const activeMenu = computed(() => {
  return route.path;
});

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm("确定退出登录吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logout();
      router.push("/login");
    })
    .catch(() => {});
};

watch(
  () => route.path,
  (newpath, oldpath) => {
    if (newpath !== "/report/generate") {
      searchQuery.value = "";
    }
  }
);

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add");
const submitLoading = ref(false);
const studentFormRef = ref(null);
const formContent = ref(null);
const activeSection = ref(0);

// 表单部分相关
const formSections = [
  "个人基础信息",
  "本科成绩情况",
  "英语基础",
  "目标院校倾向",
  "考研成绩预估",
];

// 下拉搜索相关
const schoolSearchLoading = ref(false);
const schoolOptions = ref([]);
const schoolSearchTimeout = ref(null);
const schoolSearchCache = {};

const majorSearchLoading = ref(false);
const majorOptions = ref([]);
const majorSearchTimeout = ref(null);
const majorSearchCache = {};

const targetMajorSearchLoading = ref(false);
const targetMajorOptions = ref([]);
const targetMajorSearchTimeout = ref(null);
const targetMajorSearchCache = {};

// 标签选项
const tagOptions = ref([]);

// 学员表单
const studentForm = reactive({
  // 基本信息
  id: "",
  name: "",
  sex: "", // 1-男, 2-女, 3-其他
  phone: "",
  teacherPhone: "",
  undergraduateSchool: "",
  undergraduateMajor: "",
  targetMajor: "",
  majorCode: "",
  isPostgraduate: false, // 是否研究生
  examYear: "", // 考研届数: 2026, 2027, 2028, 2029, "定向"
  isMultiDisciplinary: "", // 是否跨专业: 1-是, 2-否

  // 本科成绩情况
  mathScores: [
    { id: 1, title: "高数(上)期末考试", score: "" },
    { id: 2, title: "高数(下)期末考试", score: "" },
    { id: 3, title: "积分论期末考试", score: "" },
    { id: 4, title: "线性代数期末考试", score: "" },
  ],
  specializedCourses: [
    { id: 1, title: "专业课一", name: "", score: "" },
    { id: 2, title: "专业课二", name: "", score: "" },
    { id: 3, title: "专业课三", name: "", score: "" },
  ],

  // 英语基础
  englishScore: "", // 高考英语成绩
  cetLevel: "", // 四六级: "四级", "六级"
  specialEnglishLevel: "", // 专四专八: "专四", "专八"
  tofelIelts: "", // 托福/雅思
  hasEnglishTraining: "", // 是否参加培训班: 1-是, 2-否

  // 目标院校倾向
  targetRegion: "", // 地区倾向: "A区", "B区"
  targetProvinces: [], // 省份选择
  targetSchool: "", // 梦校
  schoolLevel: "", // 院校层次: "985", "211", "双一流"

  // 考研成绩估算
  politics: "", // 政治
  englishType: "", // 英语类型: "英语一", "英语二"
  mathType: "", // 数学类型: "数学一", "数学二", "数学三"
  estimatedScores: [
    // 专业课估分
    { id: 1, title: "专业课一", name: "", score: "" },
    { id: 2, title: "专业课二", name: "", score: "" },
    { id: 3, title: "专业课三", name: "", score: "" },
  ],
  totalScore: "", // 总分

  // 个性化需求
  personalNeeds: "",

  // 标签
  tags: [], // 标签列表
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: "请输入学员姓名", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  undergraduateSchool: [
    { required: true, message: "请选择本科院校", trigger: "change" },
  ],
  undergraduateMajor: [
    { required: true, message: "请选择本科专业", trigger: "change" },
  ],
  targetMajor: [
    { required: true, message: "请选择目标专业", trigger: "change" },
  ],
};

// 获取所有标签
const fetchAllTags = () => {
  getAllTags()
    .then((res) => {
      console.log("获取标签结果:", res);
      if (res.code === 0 && res.data) {
        // 处理标签数据，构建树形结构
        // 合并所有层级的标签
        const allTags = [
          ...(res.data.first || []),
          ...(res.data.second || []),
          ...(res.data.third || []),
        ];

        const tags = allTags.map((tag) => ({
          value: tag.id.toString(),
          label: tag.name,
          level: tag.level,
          parent_id: tag.parent_id,
          class: getTagClass(tag.level),
        }));
        tagOptions.value = tags;
      }
    })
    .catch((err) => {
      console.error("获取标签失败", err);
    });
};

// 根据标签级别获取样式类名
const getTagClass = (level) => {
  if (level === 1) {
    return "tag-default"; // 一级标签使用默认样式
  } else if (level === 2) {
    // 二级标签随机颜色
    const colors = ["tag-orange", "tag-purple", "tag-green", "tag-blue"];
    return colors[Math.floor(Math.random() * colors.length)];
  } else if (level === 3) {
    // 三级标签随机颜色
    const colors = ["tag-orange", "tag-purple", "tag-green", "tag-blue"];
    return colors[Math.floor(Math.random() * colors.length)];
  }
  return "tag-default";
};

// 处理添加学员
const handleAddStudent = () => {
  dialogType.value = "add";
  // 重置表单 - 确保所有字段都有默认值，避免显示null
  Object.keys(studentForm).forEach((key) => {
    if (key === "tags" || key === "targetProvinces") {
      studentForm[key] = [];
    } else if (key === "mathScores") {
      studentForm[key] = [
        { id: 1, title: "高数(上)期末考试", score: "" },
        { id: 2, title: "高数(下)期末考试", score: "" },
        { id: 3, title: "积分论期末考试", score: "" },
        { id: 4, title: "线性代数期末考试", score: "" },
      ];
    } else if (key === "specializedCourses") {
      studentForm[key] = [
        { id: 1, title: "专业课一", name: "", score: "" },
        { id: 2, title: "专业课二", name: "", score: "" },
        { id: 3, title: "专业课三", name: "", score: "" },
      ];
    } else if (key === "estimatedScores") {
      studentForm[key] = [
        { id: 1, title: "专业课一", name: "", score: "" },
        { id: 2, title: "专业课二", name: "", score: "" },
        { id: 3, title: "专业课三", name: "", score: "" },
      ];
    } else {
      // 确保所有其他字段都是空字符串，而不是null或undefined
      studentForm[key] = "";
    }
  });

  // 清空选项
  schoolOptions.value = [];
  majorOptions.value = [];
  targetMajorOptions.value = [];

  // 获取标签数据
  if (tagOptions.value.length === 0) {
    fetchAllTags();
  }

  // 显示对话框
  dialogVisible.value = true;
};

// 滚动到指定部分
const scrollToSection = (index) => {
  // 设置活动部分
  activeSection.value = index;

  // 延迟一下，确保DOM已经渲染完成
  setTimeout(() => {
    // 使用ID选择器更精确地定位到对应的部分
    const sectionId = `section-${index}`;
    const section = document.getElementById(sectionId);

    if (section && formContent.value) {
      // 滚动到对应的部分，并添加一些偏移量以获得更好的视觉效果
      formContent.value.scrollTop = section.offsetTop - 20;
    }
  }, 50);
};

// 搜索本科院校
const remoteSearchSchool = (query) => {
  if (!query) {
    schoolOptions.value = [];
    return;
  }

  schoolSearchLoading.value = true;
  console.log("搜索本科院校:", query);

  // 检查缓存中是否已有该查询结果
  if (schoolSearchCache[query]) {
    schoolOptions.value = schoolSearchCache[query];
    schoolSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(schoolSearchTimeout.value);
  schoolSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("搜索本科院校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          schoolOptions.value = options;
          // 添加到缓存
          schoolSearchCache[query] = options;
        } else {
          schoolOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取学校列表失败", err);
        schoolOptions.value = [];
      })
      .finally(() => {
        schoolSearchLoading.value = false;
      });
  }, 300);
};

// 搜索本科专业
const remoteSearchMajor = (query) => {
  if (!studentForm.undergraduateSchool) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  majorSearchLoading.value = true;
  console.log("搜索本科专业:", studentForm.undergraduateSchool, query);

  // 生成缓存键，包含学校ID和查询词
  const cacheKey = `${studentForm.undergraduateSchool}_${query}`;

  // 检查缓存中是否已有该查询结果
  if (majorSearchCache[cacheKey]) {
    majorOptions.value = majorSearchCache[cacheKey];
    majorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(majorSearchTimeout.value);
  majorSearchTimeout.value = setTimeout(() => {
    searchMajor(studentForm.undergraduateSchool, query || "")
      .then((res) => {
        console.log("搜索本科专业结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.major_name || item.name,
          }));
          majorOptions.value = options;
          // 添加到缓存
          majorSearchCache[cacheKey] = options;
        } else {
          majorOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取专业列表失败", err);
        majorOptions.value = [];
      })
      .finally(() => {
        majorSearchLoading.value = false;
      });
  }, 300);
};

// 搜索目标专业
const remoteSearchTargetMajor = (query) => {
  if (!query) {
    targetMajorOptions.value = [];
    return;
  }

  targetMajorSearchLoading.value = true;
  console.log("搜索目标专业:", query);

  // 检查缓存中是否已有该查询结果
  if (targetMajorSearchCache[query]) {
    targetMajorOptions.value = targetMajorSearchCache[query];
    targetMajorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(targetMajorSearchTimeout.value);
  targetMajorSearchTimeout.value = setTimeout(() => {
    searchTargetMajor(query)
      .then((res) => {
        console.log("搜索目标专业结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id?.toString() || item.name,
            label: item.name,
            code: item.code,
          }));
          targetMajorOptions.value = options;
          // 添加到缓存
          targetMajorSearchCache[query] = options;
        } else {
          targetMajorOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取目标专业列表失败", err);
        targetMajorOptions.value = [];
      })
      .finally(() => {
        targetMajorSearchLoading.value = false;
      });
  }, 300);
};

// 监听本科院校变更
const handleSchoolChange = () => {
  // 清空专业选择
  studentForm.undergraduateMajor = "";
  // 清空专业选项
  majorOptions.value = [];

  // 如果选择了本科院校，可以尝试加载该院校的专业列表
  if (studentForm.undergraduateSchool) {
    remoteSearchMajor("");
  }
};

// 提交表单
const submitForm = async () => {
  if (!studentFormRef.value) return;

  try {
    await studentFormRef.value.validate();

    submitLoading.value = true;

    // 准备提交的数据
    const formData = {
      ...studentForm,
      mathScoresJson: JSON.stringify(studentForm.mathScores),
      specializedCoursesJson: JSON.stringify(studentForm.specializedCourses),
      estimatedScoresJson: JSON.stringify(studentForm.estimatedScores),
    };

    // 调用添加学员API
    addStudent(formData)
      .then((res) => {
        console.log("添加学员结果:", res);
        if (res.code === 0) {
          ElMessage.success("添加学员成功");
          dialogVisible.value = false;

          // 如果当前在学员列表页面，可以刷新列表
          if (route.path === "/student/list") {
            // 这里可以触发刷新列表的事件
          }
        } else {
          ElMessage.error(res.msg || "添加学员失败");
        }
      })
      .catch((err) => {
        console.error("添加学员失败", err);
        ElMessage.error("添加学员失败，请稍后重试");
      })
      .finally(() => {
        submitLoading.value = false;
      });
  } catch (error) {
    console.error("表单验证失败", error);
    submitLoading.value = false;
  }
};
</script>

<style scoped>
/* 布局容器样式 */
.layout-container {
  height: 100vh;
  width: 100%;
}

.el-container {
  height: 100%;
}

.right-container {
  flex-direction: column;
  height: 100%;
}

/* 顶部导航栏样式 */
.header-section {
  margin: 0 20px;
  border-radius: 0px 0px 12px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

/* 侧边栏样式 */
.aside {
  background-color: #fff;
  transition: width 0.3s;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 100vh;
  background-image: url("@/assets/images/sider_tab_bg.png");
  background-repeat: no-repeat;
  background-position: bottom center;
  background-size: contain;
  /* 确保侧边栏高度为100%视口高度 */
}

/* 侧边栏徽标容器 */
.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background-color: #fff;
  color: #333;
  overflow: hidden;
}

.sidebar-logo {
  width: 100%;
  margin-right: 8px;
}

/* 顶部导航栏样式 */
.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 30px;
}

.search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 500px;
}

.search-box {
  width: 320px;
  margin-right: 30px;
}

.search-input :deep(.el-input__inner) {
  border-radius: 20px;
  padding-left: 15px;
}

.search-icon {
  color: #999;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.create-btn {
  margin-right: 30px;
  background-color: #16b788;
  border-color: #16b788;
  border-radius: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.user-avatar {
  margin-right: 8px;
  background-color: #e0f2ff;
}

.welcome-text {
  color: #333;
  margin-right: 8px;
}

.logout-text {
  color: #16b788;
  cursor: pointer;
}

.main {
  background-color: #f0f2f5;
  padding: 16px;
  height: calc(100vh - 56px - 32px);
  /* 减去顶部导航栏的高度和margin */
  overflow: auto;
}

/* 过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 自定义菜单样式 */
.custom-menu {
  border-right: none;
}

.custom-menu :deep(.el-menu-item.is-active) {
  background-color: #e6f7f1;
  border-radius: 10px;
  color: #16b788;
  font-weight: bold;
}
.custom-menu :deep(.el-menu-item.is-active)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 80%;
  background: #1bb394;
  border-radius: 9px 9px 9px 9px;
}

.custom-menu :deep(.el-menu-item:hover) {
  background-color: #f5f5f5;
}

.custom-menu-item {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-menu :deep(.menu-icon) {
  color: #333;
}

.custom-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title .menu-icon {
  color: #16b788;
}

/* 移除文字的高亮颜色 */
.custom-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title {
  display: flex;
  align-items: center;
  color: #333 !important;
  justify-content: center !important;
}
.custom-menu :deep(.el-sub-menu) > .el-sub-menu__title {
  display: flex;
  align-items: center;
  color: #333 !important;
  justify-content: center !important;
}

/* 去掉默认的active-text-color效果 */
.el-menu {
  --el-menu-active-color: #333;
}

/* 学员表单对话框样式 */
.student-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.dialog-container {
  display: flex;
  height: 70vh;
}

.dialog-sidebar {
  width: 200px;
  background-color: #f5f7fa;
  padding: 20px 0;
  border-right: 1px solid #e6e6e6;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.sidebar-item:hover {
  background-color: #e6f7f1;
}

.sidebar-item.active {
  background-color: #e6f7f1;
  border-right: 3px solid #16b788;
}

.sidebar-item-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #16b788;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-weight: bold;
}

.sidebar-item.active .sidebar-item-number {
  background-color: #16b788;
}

.sidebar-item-text {
  font-size: 14px;
  color: #333;
}

.dialog-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.student-form {
  padding-bottom: 50px;
}

.action-button {
  background-color: #16b788;
  border-color: #16b788;
}

/* 学员搜索建议项样式 */
.student-suggestion-item {
  padding: 8px 0;
}

.student-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.student-info {
  font-size: 12px;
  color: #999;
  display: flex;
}

.student-info span {
  margin-right: 10px;
}

/* 自动完成组件样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 20px;
  padding-left: 15px;
}

/* 确保下拉菜单宽度与输入框一致 */
:deep(.student-suggestions) {
  width: 320px !important;
  z-index: 9999 !important;
}

:deep(.student-suggestions .el-scrollbar__wrap) {
  max-height: 280px;
}

:deep(.student-suggestions .el-autocomplete-suggestion__list) {
  padding: 8px 0;
}

/* 鼠标悬停效果 */
:deep(.student-suggestions .el-autocomplete-suggestion__list li:hover) {
  background-color: #f5f7fa;
}

:deep(.student-suggestions .el-autocomplete-suggestion__list li.highlighted) {
  background-color: #f5f7fa;
}

/* 确保下拉菜单显示在其他元素之上 */
.el-popper {
  z-index: 9999 !important;
}
</style>
