<?php
/**
 * Here is your custom functions.
 */

/**
 * 发送GET请求
 *
 * @param string $url 请求URL
 * @param array $params 请求参数
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_get($url, $params = [], $headers = [], $timeout = 10)
{
    
    // 如果有参数，将参数添加到URL
    if (!empty($params)) {
        $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
    }

    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 设置请求头
    if (!empty($headers)) {
        $headerArray = [];
        foreach ($headers as $key => $value) {
            $headerArray[] = $key . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
    }

    // 执行请求
    $response = curl_exec($ch);
    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP GET请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $response;
}

/**
 * 发送POST请求
 *
 * @param string $url 请求URL
 * @param array|string $data 请求数据
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_post($url, $data = [], $headers = [], $timeout = 10)
{
    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 设置POST数据
    if (is_array($data)) {
        // 如果是数组，转换为查询字符串
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    } else {
        // 如果是字符串（如JSON），直接使用
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }

    // 设置请求头
    if (!empty($headers)) {
        $headerArray = [];
        foreach ($headers as $key => $value) {
            $headerArray[] = $key . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
    }

    // 执行请求
    $response = curl_exec($ch);

    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP POST请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $response;
}

/**
 * 发送JSON格式的POST请求
 *
 * @param string $url 请求URL
 * @param array $data 请求数据（将被转换为JSON）
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_post_json($url, $data = [], $headers = [], $timeout = 10)
{
    // 添加JSON相关的请求头
    $headers = array_merge([
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
    ], $headers);

    // 将数据转换为JSON
    $jsonData = json_encode($data);

    // 调用POST请求函数
    return http_post($url, $jsonData, $headers, $timeout);
}

/**
 * 获取HTTP状态码
 *
 * @param string $url 请求URL
 * @param int $timeout 超时时间（秒）
 * @return int|false 返回HTTP状态码，失败返回false
 */
function http_status_code($url, $timeout = 10)
{
    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // 不获取响应体
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 执行请求
    curl_exec($ch);

    // 获取HTTP状态码
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP状态码请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $statusCode;
}

/**
 * 清理JSON字符串，移除markdown代码块标记
 * @param string $rawString 原始字符串
 * @return string 清理后的JSON字符串
 */
function cleanJsonString($rawString) {
    $cleaned = preg_replace('/^```json\s*/', '', $rawString);
    $cleaned = preg_replace('/\s*```$/', '', $cleaned);
    return trim($cleaned);
}

/**
 * 解析JSON字符串为PHP数组（基础版本）
 * @param string $jsonString JSON字符串
 * @return array 解析后的数组
 */
function parseJsonToArray($jsonString) {
    try {
        $cleanedString = cleanJsonString($jsonString);
        $data = json_decode($cleanedString, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('JSON解析失败: ' . json_last_error_msg() . ', 原始字符串: ' . $jsonString);
            // 返回默认结构而不是抛出异常
            return [
                'recommend_list' => [],
                'high_recommend_list' => []
            ];
        }
        
        return $data;
    } catch (Exception $e) {
        error_log('JSON解析错误: ' . $e->getMessage());
        // 返回默认结构而不是抛出异常
        return [
            'recommend_list' => [],
            'high_recommend_list' => []
        ];
    }
}
