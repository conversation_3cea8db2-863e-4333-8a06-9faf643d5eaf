<?php
namespace app\model;

use think\Model;

class ReportInfo extends Model
{
    protected $table = 'ba_report_info';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 根据报告ID获取推荐院校列表
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getRecommendationsByReportId($reportId)
    {
        return self::where('report_id', $reportId)
            ->where('status', 1)
            ->order('id', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 根据学生ID获取推荐院校列表
     *
     * @param int $studentId 学生ID
     * @return array
     */
    public static function getRecommendationsByStudentId($studentId)
    {
        return self::where('student_id', $studentId)
            ->where('status', 1)
            ->order('id', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 删除报告的推荐数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 批量插入推荐院校数据
     *
     * @param array $data 推荐院校数据数组
     * @return bool
     */
    public static function insertRecommendations($data)
    {
        if (empty($data)) {
            return false;
        }

        return self::insertAll($data);
    }

    /**
     * 获取推荐院校统计信息
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getRecommendationStats($reportId)
    {
        $total = self::where('report_id', $reportId)
            ->where('status', 1)
            ->count();

        $withDifficulty = self::where('report_id', $reportId)
            ->where('status', 1)
            ->where('competition_difficulty', '<>', '')
            ->count();

        $withSuggestions = self::where('report_id', $reportId)
            ->where('status', 1)
            ->where('suggestions', '<>', '')
            ->count();

        return [
            'total' => $total,
            'with_difficulty' => $withDifficulty,
            'with_suggestions' => $withSuggestions
        ];
    }
}
