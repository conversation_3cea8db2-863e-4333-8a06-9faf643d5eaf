<template>
  <div class="student-list-container">
    <!-- 顶部统计卡片区域 -->
    <div class="stats-cards">
      <div class="stat-card left-card">
        <div class="stat-content">
          <span class="stat-label">生成报告数：</span>
          <span class="stat-value">{{ reportCount }}人次</span>
        </div>
      </div>
      <div class="stat-card right-card">
        <div class="stat-content">
          <span class="stat-label">容量：</span>
          <span class="stat-value">1000人次</span>
        </div>
      </div>
      <div class="tip-card"></div>
    </div>

    <!-- 学员搜索区域 -->
    <div class="search-section">
      <div class="section-header">
        <img src="@/assets/images/tag.png" alt="标签图标" />
        <span class="section-title">学员搜索</span>
      </div>

      <div class="search-form">
        <div class="form-item">
          <span class="item-label">姓名</span>
          <el-autocomplete
            v-model="searchForm.name"
            :fetch-suggestions="queryStudentNames"
            placeholder="张三"
            clearable
            :trigger-on-focus="false"
            @select="handleNameSelect"
          ></el-autocomplete>
        </div>
        <div class="form-item">
          <span class="item-label">本科院校</span>
          <el-select
            v-model="searchForm.school_name"
            placeholder="中国科学技术大学"
            clearable
            filterable
            remote
            :remote-method="querySchools"
            :loading="schoolLoading"
          >
            <el-option
              v-for="item in schoolOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div class="form-item">
          <span class="item-label">本科专业</span>
          <el-select
            v-model="searchForm.major_name"
            placeholder="通信工程"
            clearable
            filterable
            remote
            :remote-method="queryMajors"
            :loading="majorLoading"
          >
            <el-option
              v-for="item in majorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div class="form-item grade_class">
          <span class="item-label">年级</span>
          <el-select v-model="searchForm.class" placeholder="2023" clearable>
            <el-option
              v-for="item in years"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-button" @click="handleSearch"
            >查询</el-button
          >
          <el-button type="primary" class="search-button" @click="resetSearch"
            >重置</el-button
          >
        </div>

        <span class="grid-display" @click="toggleDisplay">
          <i class="el-icon-menu"></i>
        </span>
      </div>
    </div>

    <!-- 学员列表区域 -->
    <div class="student-list-section">
      <div class="section-header">
        <img src="@/assets/images/tag.png" alt="标签图标" />
        <span class="section-title">学员列表</span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        class="custom-table"
      >
        <el-table-column
          prop="id"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="year"
          label="年份"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="undergraduate"
          label="本科院校"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="major"
          label="本科专业"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="240" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.pdf_url"
              @click="handlePreview(scope.row.pdf_url)"
              class="action-button up-button"
              size="small"
              >下载</el-button
            >
            <el-button class="action-button up-button" size="small"
              >上传</el-button
            >
            <el-button class="action-button edit-button" size="small"
              >修改</el-button
            >
            <el-button class="action-button delete-button" size="small"
              >二维码</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div style="margin-top: 20px; display: flex; justify-content: flex-end">
        <el-pagination
          background
          layout="prev, pager, next, jumper, ->, total"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { searchSchool, searchMajor, searchTargetMajor } from "@/api/school";
import { getStudentList } from "@/api/student";
import { reportList, getExamYears, getReportNum } from "@/api/report"; // 导入 getReportNum
import { ElMessage } from "element-plus";

// 搜索表单
const searchForm = reactive({
  name: "",
  school_name: "",
  major_name: "",
  class: "",
  page: 1,
  limit: 10,
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const reportCount = ref(0); // 新增响应式变量存储报告数

//获取生成报告数
const fetchReportCount = async () => {
  try {
    const res = await getReportNum();
    if (res.code === 0 && res.data) {
      reportCount.value = res.data.count;
    }
  } catch (error) {
    console.error("获取报告数量失败:", error);
    ElMessage.error("获取报告数量失败");
  }
};

// 学生姓名搜索相关
const queryStudentNames = (queryString, callback) => {
  if (queryString.length < 1) {
    callback([]);
    return;
  }
  getStudentList({ name: queryString, page: 1, limit: 50 }).then((res) => {
    const results = res.data
      .map((item) => ({
        value: item.name,
      }))
      .filter((item) => item.value.includes(queryString));
    callback(results);
  });

  // 从后端获取学生姓名数据
  // 这里应该调用学生搜索API，但为了简化，我们使用模拟数据
  // // 实际项目中应该替换为 getStudentList API 调用
  // setTimeout(() => {
  //   const results = [
  //     { value: "张三" },
  //     { value: "张三丰" },
  //     { value: "张三星" },
  //     { value: "李四" },
  //     { value: "王五" },
  //   ].filter((item) => item.value.includes(queryString));

  // }, 200);
};

const handleNameSelect = (item) => {
  searchForm.name = item.value;
};

// 学校搜索相关
const schoolOptions = ref([]);
const schoolLoading = ref(false);
const schoolSearchCache = ref({}); // 添加缓存

const querySchools = async (query) => {
  if (query.length < 1) {
    schoolOptions.value = [];
    return;
  }

  // 检查缓存中是否已有该查询结果
  if (schoolSearchCache.value[query]) {
    schoolOptions.value = schoolSearchCache.value[query];
    return;
  }

  schoolLoading.value = true;
  try {
    // 调用API获取学校数据
    const res = await searchSchool(query);
    console.log("搜索学校结果:", res);

    if (res && res.code === 0 && res.data) {
      // 转换为下拉菜单需要的格式
      const options = res.data.map((item) => ({
        value: item.id.toString(),
        label: item.name,
      }));

      schoolOptions.value = options;
      // 添加到缓存
      schoolSearchCache.value[query] = options;
    } else {
      // 模拟数据，实际项目中应该使用API返回的数据
      const mockData = [
        { value: "1", label: "中国科学技术大学" },
        { value: "2", label: "北京大学" },
        { value: "3", label: "清华大学" },
        { value: "4", label: "复旦大学" },
        { value: "5", label: "南京大学" },
      ].filter((item) => item.label.includes(query));

      schoolOptions.value = mockData;
      // 添加到缓存
      schoolSearchCache.value[query] = mockData;
    }
  } catch (error) {
    console.error("获取学校数据失败:", error);
    // 使用模拟数据作为备选
    const mockData = [
      { value: "1", label: "中国科学技术大学" },
      { value: "2", label: "北京大学" },
      { value: "3", label: "清华大学" },
    ].filter((item) => item.label.includes(query));

    schoolOptions.value = mockData;
    // 添加到缓存
    schoolSearchCache.value[query] = mockData;
  } finally {
    schoolLoading.value = false;
  }
};

// 专业搜索相关
const majorOptions = ref([]);
const majorLoading = ref(false);
const majorSearchCache = ref({}); // 添加缓存

const queryMajors = async (query) => {
  if (query.length < 1) {
    majorOptions.value = [];
    return;
  }

  // 检查是否选择了学校
  if (!searchForm.undergraduate) {
    // 如果没有选择学校，使用通用专业搜索
    const cacheKey = `general_${query}`;

    // 检查缓存中是否已有该查询结果
    if (majorSearchCache.value[cacheKey]) {
      majorOptions.value = majorSearchCache.value[cacheKey];
      return;
    }

    majorLoading.value = true;
    try {
      // 调用API获取专业数据 - 使用searchTargetMajor而不是searchMajor
      const res = await searchTargetMajor(query);
      console.log("搜索专业结果:", res);

      if (res && res.code === 0 && res.data) {
        // 转换为下拉菜单需要的格式
        const options = res.data.map((item) => ({
          value: item.code || item.id.toString(),
          label: item.name,
        }));

        majorOptions.value = options;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = options;
      } else {
        // 模拟数据
        const mockData = [
          { value: "1", label: "通信工程" },
          { value: "2", label: "计算机科学与技术" },
          { value: "3", label: "软件工程" },
          { value: "4", label: "电子信息工程" },
          { value: "5", label: "人工智能" },
        ].filter((item) => item.label.includes(query));

        majorOptions.value = mockData;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = mockData;
      }
    } catch (error) {
      console.error("获取专业数据失败:", error);
      // 使用模拟数据作为备选
      const mockData = [
        { value: "1", label: "通信工程" },
        { value: "2", label: "计算机科学与技术" },
        { value: "3", label: "软件工程" },
      ].filter((item) => item.label.includes(query));

      majorOptions.value = mockData;
      // 添加到缓存
      majorSearchCache.value[cacheKey] = mockData;
    } finally {
      majorLoading.value = false;
    }
  } else {
    // 如果选择了学校，使用学校专业搜索
    const schoolId = searchForm.undergraduate;
    const cacheKey = `${schoolId}_${query}`;

    // 检查缓存中是否已有该查询结果
    if (majorSearchCache.value[cacheKey]) {
      majorOptions.value = majorSearchCache.value[cacheKey];
      return;
    }

    majorLoading.value = true;
    try {
      // 调用API获取专业数据 - 使用正确的参数
      const res = await searchMajor(schoolId, query);
      console.log("搜索学校专业结果:", res);

      if (res && res.code === 0 && res.data) {
        // 转换为下拉菜单需要的格式
        const options = res.data.map((item) => ({
          value: item.id.toString(),
          label: item.major_name || item.name,
        }));

        majorOptions.value = options;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = options;
      } else {
        // 模拟数据
        const mockData = [
          { value: "1", label: "通信工程" },
          { value: "2", label: "计算机科学与技术" },
          { value: "3", label: "软件工程" },
          { value: "4", label: "电子信息工程" },
          { value: "5", label: "人工智能" },
        ].filter((item) => item.label.includes(query));

        majorOptions.value = mockData;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = mockData;
      }
    } catch (error) {
      console.error("获取专业数据失败:", error);
      // 使用模拟数据作为备选
      const mockData = [
        { value: "1", label: "通信工程" },
        { value: "2", label: "计算机科学与技术" },
        { value: "3", label: "软件工程" },
      ].filter((item) => item.label.includes(query));

      majorOptions.value = mockData;
      // 添加到缓存
      majorSearchCache.value[cacheKey] = mockData;
    } finally {
      majorLoading.value = false;
    }
  }
};

// 切换显示模式
const displayMode = ref("table");
const toggleDisplay = () => {
  displayMode.value = displayMode.value === "table" ? "grid" : "table";
};

// 获取表格数据
const getList = async () => {
  loading.value = true;
  await reportList(searchForm).then((res) => {
    tableData.value = res.data.data;
    total.value = res.data.total;
    loading.value = false;
  });
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  getList();
};

//下载pdf
const handlePreview = (url) => {
  window.open(url, "_blank");
};

// 重置搜索
const resetSearch = () => {
  // 清空搜索表单
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = "";
  });

  // 清空下拉选项
  schoolOptions.value = [];
  majorOptions.value = [];

  // 重新搜索
  handleSearch();
};

// 页面加载时获取数据
onMounted(() => {
  getList();
  getYears();
  fetchReportCount(); // 调用获取报告数的方法
});
const years = ref([]);
const getYears = () => {
  getExamYears().then((res) => {
    console.log(res);
    years.value = res.data;
  });
};
// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  getList();
};
</script>

<style scoped>
.student-list-container {
  background-color: #fff;
  padding: 20px;
}

/* 顶部统计卡片样式 */
.stats-cards {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.stat-card {
  width: 558px;
  height: 120px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  color: #fff;
  position: relative;
}

.left-card {
  background: linear-gradient(to right, #e0c7ff, #f9f3ff);
  margin-right: 10px;
  background-image: url("@/assets/images/g_list_left_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  color: #9a5fdf;
}

.right-card {
  background: linear-gradient(to right, #ffd6a5, #fff9f0);
  margin-left: 10px;
  background-image: url("@/assets/images/g_list_right_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  color: #ffa03e;
  margin-left: 54px;
}
.tip-card {
  width: 190px;
  height: 30px;
  background: linear-gradient(to right, #ffd6a5, #fff9f0);
  margin-left: 10px;
  background-image: url("@/assets/images/list-tip.png");
  background-size: cover;
  background-position: right;
  color: #ffa03e;
  justify-content: space-between;
}
.stat-content {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 22px;
  color: #9d50ff;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.stat-label {
  font-size: 16px;
  margin-right: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
}

.warning-text {
  flex: 1;
  color: #ff4d4f;
  font-size: 14px;
  margin-right: 20px;
}

/* 搜索区域样式 */
.search-section,
.student-list-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 15px 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  img {
    width: 26px;
    height: 22px;
    margin-right: 6px;
  }
}

.section-icon {
  width: 10px;
  height: 10px;
  background-color: #18b3b3;
  border-radius: 50%;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.search-form {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 10px;
}
.grade_class {
  width: 200px;
}
.item-label {
  margin-right: 8px;
  white-space: nowrap;
}

.search-button {
  background-color: #18b3b3;
  border-color: #18b3b3;
  margin-left: 10px;
}

.grid-display {
  margin-left: auto;
  cursor: pointer;
  font-size: 20px;
}

/* 表格样式 */
.custom-table {
  margin-top: 15px;
}

.action-button {
  padding: 4px 8px;
  margin: 0 3px;
}

.up-button {
  color: #18b3b3;
  border: none;
}

.edit-button {
  color: #18b3b3;
  border: none;
}

.delete-button {
  color: #18b3b3;
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }

  .stat-card {
    margin: 0 0 10px 0;
  }

  .search-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-item {
    width: 100%;
    margin-right: 0;
  }
}
</style>
