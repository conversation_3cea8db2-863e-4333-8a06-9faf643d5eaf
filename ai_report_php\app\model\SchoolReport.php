<?php
namespace app\model;

use think\Model;

class SchoolReport extends Model
{
    protected $table = 'ba_school_report';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 地区倾向获取器
     * 将JSON字符串转换为数组
     * 数据格式: ["A", "B"]
     *
     * @param mixed $value 数据库中存储的JSON字符串
     * @return array 转换后的数组
     */
    public function getRegionPreferenceAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 地区倾向修改器
     * 将数组转换为JSON字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或JSON字符串
     * @return string 转换后的JSON字符串
     */
    public function setRegionPreferenceAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 省份选择获取器
     * 将逗号分隔的字符串转换为数组
     * 数据格式: "北京市,上海市,广东省,..."
     *
     * @param mixed $value 数据库中存储的逗号分隔字符串
     * @return array 转换后的数组
     */
    public function getProvinceSelectionAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * 省份选择修改器
     * 将数组转换为逗号分隔的字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或字符串
     * @return string 转换后的逗号分隔字符串
     */
    public function setProvinceSelectionAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 学生关联
     * @return \think\model\relation\BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }
}
