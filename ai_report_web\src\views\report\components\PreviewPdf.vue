<template>
    <div class="generate-container">
        <div class="report-container">
            <!-- 报告封面头部区域 -->
            <div class="report-header">
                <div class="logo">
                    <img src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png">
                </div>
            </div>

            <div class="steps">
                <!-- 第一步：个人基础信息 -->
                <div class="step-section">
                    <div class="step-header">
                        <div class="step-title">第一部分：个人基础信息</div>
                    </div>

                    <div class="step-content">
                        <div class="step-num-tag">
                            <span>01</span>
                            <div class="tag-text">个人基础信息</div>
                        </div>

                        <div class="form-grid">
                            <div class="form-item">
                                <div class="item-label">学员姓名</div>
                                <el-input v-model="reportForm.name" placeholder="请输入学员姓名"></el-input>
                            </div>

                            <div class="form-item">
                                <div class="item-label">性别</div>
                                <el-select v-model="reportForm.sex" placeholder="请选择性别" class="full-width">
                                    <el-option label="男" value="1"></el-option>
                                    <el-option label="女" value="2"></el-option>
                                    <el-option label="其他" value="3"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">本科院校</div>
                                <el-select v-model="reportForm.undergraduateSchool" placeholder="输入关键字搜索院校" filterable remote reserve-keyword :remote-method="remoteSearchCollege" :loading="collegeSearchLoading" class="full-width" @change="handleCollegeChange">
                                    <el-option v-for="item in collegeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">本科专业</div>
                                <el-select v-model="reportForm.undergraduateMajor" placeholder="输入关键字搜索专业" filterable remote reserve-keyword :remote-method="remoteSearchMajor" :loading="majorSearchLoading" class="full-width" :disabled="!reportForm.undergraduateSchool">
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">目标专业</div>
                                <el-select v-model="reportForm.targetMajor" placeholder="输入关键字搜索专业" filterable remote reserve-keyword :remote-method="remoteSearchTargetMajor" :loading="targetMajorSearchLoading" class="full-width" @change="handleTargetMajorChange">
                                    <el-option v-for="item in targetMajorOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">专业代码</div>
                                <el-input v-model="reportForm.majorCode" placeholder="请输入专业代码"></el-input>
                            </div>

                            <div class="form-item">
                                <div class="item-label">联系方式</div>
                                <el-input v-model="reportForm.phone" placeholder="请输入联系方式"></el-input>
                            </div>

                            <div class="form-item">
                                <div class="item-label">考研年份</div>
                                <el-select v-model="reportForm.examYear" placeholder="请选择考研年份" class="full-width">
                                    <el-option label="2027" value="2027"></el-option>
                                    <el-option label="2026" value="2026"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">跨专业</div>
                                <el-select v-model="reportForm.isMultiDisciplinary" placeholder="请选择跨专业" class="full-width">
                                    <el-option label="是" value="1"></el-option>
                                    <el-option label="否" value="2"></el-option>
                                </el-select>
                            </div>

                            <div class="form-item">
                                <div class="item-label">培养方式</div>
                                <el-select v-model="reportForm.educationalStyle" placeholder="请选择培养方式" class="full-width">
                                    <el-option label="全日制" value="0"></el-option>
                                    <el-option label="非全日制" value="1"></el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二步：本科成绩情况 -->
                <div class="step-section">
                    <div class="step-content">
                        <div class="step-container">
                            <div class="step-num-tag">
                                <span>02</span>
                                <div class="tag-text">本科成绩情况</div>
                            </div>
                            <div class="create-btn">
                                <el-button type="primary" class="action-button" @click="openAddScoreDialog">创建</el-button>
                            </div>
                        </div>

                        <div class="score-grid">
                            <div class="score-row">
                                <div class="score-item" v-for="item in scoreInfo" :key="item.id" :draggable="true" @dragstart="handleDragStart($event, item)" @dragover.prevent="handleDragOver($event)" @drop="handleDrop($event, item)" @dragend="handleDragEnd">
                                    <div class="score-label">{{ item.title }}</div>
                                    <div class="score-input-container">
                                        <el-input v-model="item.score" placeholder="140"></el-input>
                                    </div>
                                    <el-icon @click="delScoreInfo(item.id)" class="score-close" color="#1BB394" size="14">
                                        <Close />
                                    </el-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第三步：英语基础 -->
                <div class="step-section">
                    <div class="step-content">
                        <div class="step-num-tag">
                            <span>03</span>
                            <div class="tag-text">英语基础</div>
                        </div>

                        <div class="english-grid">
                            <div class="english-row">
                                <div class="english-item">
                                    <div class="english-label">高考英语成绩</div>
                                    <el-input v-model="reportForm.englishScore" placeholder="请输入高考英语成绩" class="full-width"></el-input>
                                </div>

                                <div class="english-item">
                                    <div class="english-label">大学四级</div>
                                    <el-input v-model="reportForm.cet4" placeholder="请输入大学四级成绩" class="full-width"></el-input>
                                </div>

                                <div class="english-item">
                                    <div class="english-label">大学六级</div>
                                    <el-input v-model="reportForm.cet6" placeholder="请输入大学六级成绩" class="full-width"></el-input>
                                </div>

                                <div class="english-item">
                                    <div class="english-label">托福</div>
                                    <el-input v-model="reportForm.tofelScore" placeholder="请输入托福成绩" class="full-width"></el-input>
                                </div>
                            </div>

                            <div class="english-row">
                                <div class="english-item">
                                    <div class="english-label">雅思</div>
                                    <el-input v-model="reportForm.ieltsScore" placeholder="请输入雅思成绩" class="full-width"></el-input>
                                </div>

                                <div class="english-item">
                                    <div class="english-label">英语能力</div>
                                    <el-select v-model="reportForm.englishLevel" placeholder="请选择英语能力" class="full-width">
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="良好" value="良好"></el-option>
                                        <el-option label="优秀" value="优秀"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四步：目标院校梯度 -->
            <div class="step-section" style="padding: 0 160px">
                <div class="step-content">
                    <div class="step-num-tag">
                        <span>04</span>
                        <div class="tag-text">目标院校梯度</div>
                    </div>

                    <div class="school-grid">
                        <div class="form-item">
                            <div class="item-label">地区倾向</div>
                            <el-select v-model="reportForm.region" placeholder="请选择地区倾向" multiple class="full-width">
                                <el-option label="A区" value="A区"></el-option>
                                <el-option label="B区" value="B区"></el-option>
                            </el-select>
                        </div>

                        <div class="form-item">
                            <div class="item-label">省份选择</div>
                            <el-select v-model="reportForm.intendedSchools" placeholder="请选择省份" multiple class="full-width">
                                <el-option v-for="item in filteredProvinceData" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </div>

                        <div class="form-item">
                            <div class="item-label">梦校</div>
                            <el-select v-model="reportForm.targetSchool" filterable remote reserve-keyword placeholder="输入关键字搜索院校" :remote-method="remoteSearchDreamSchool" :loading="dreamSchoolSearchLoading" class="full-width" @change="
                                (val) => {
                                    if (val) {
                                        const selected = dreamSchoolOptions.find(
                                            (item) => item.value === val
                                        );
                                        if (selected) {
                                            reportForm.targetSchoolName = selected.label;
                                        }
                                    } else {
                                        reportForm.targetSchoolName = '';
                                    }
                                }
                            ">
                                <el-option v-for="item in dreamSchoolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </div>

                        <div class="form-item">
                            <div class="item-label">院校层次</div>
                            <el-select v-model="reportForm.schoolLevel" placeholder="请选择院校层次" class="full-width">
                                <el-option label="985" value="985"></el-option>
                                <el-option label="211" value="211"></el-option>
                                <el-option label="双一流" value="双一流"></el-option>
                                <el-option label="双非" value="双非"></el-option>
                            </el-select>
                        </div>

                        <div class="form-item wide-item">
                            <div class="item-label">专业课指定参考书</div>
                            <el-input v-model="reportForm.referenceBooks" placeholder="请输入专业课指定参考书"></el-input>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第五步：考研成绩预估 -->
            <div class="step-section" style="padding: 0 160px">
                <div class="step-content">
                    <div class="step-num-tag">
                        <span>05</span>
                        <div class="tag-text">考研成绩预估</div>
                    </div>

                    <div class="score-table">
                        <div class="table-header">
                            <div class="th-cell">政治</div>
                            <div class="th-cell">
                                <el-select class="sel-no-border center-select" v-model="englishMajor" placeholder="请选择英语" size="large" popper-class="center-select-dropdown">
                                    <el-option v-for="item in EnglishMajorOptions" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </div>
                            <div class="th-cell self-score">
                                <el-select v-model="mathMajor" placeholder="请选择数学" size="large" class="sel-no-border center-select" popper-class="center-select-dropdown">
                                    <el-option v-for="item in MathMajorOptions" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </div>
                            <div class="th-cell">专业课</div>
                            <div class="th-cell">总分</div>
                        </div>
                        <div class="table-row-score">
                            <div class="td-cell">
                                <el-input v-model="reportForm.politics" class="table-input" @input="calculateTotalScore"></el-input>
                            </div>
                            <div class="td-cell">
                                <el-input v-model="reportForm.englishType" class="table-input" @input="calculateTotalScore"></el-input>
                            </div>
                            <div class="td-cell">
                                <el-input v-model="reportForm.mathType" class="table-input" @input="calculateTotalScore"></el-input>
                            </div>
                            <div class="td-cell">
                                <el-input v-model="reportForm.professional" class="table-input" @input="calculateTotalScore"></el-input>
                            </div>
                            <div class="td-cell">
                                <el-input v-model="reportForm.totalScore" class="table-input" readonly></el-input>
                            </div>
                        </div>
                    </div>

                    <div class="personal-demands">
                        <div class="demands-label">个性化需求</div>
                        <el-input v-model="reportForm.personalNeeds" type="textarea" :rows="1" placeholder="请输入个性化需求" class="demands-input"></el-input>
                    </div>

                    <div class="expertise-advice">
                        <div class="advice-label">薄弱模块</div>
                        <el-input v-model="reportForm.weakModules" type="textarea" :rows="1" placeholder="请输入薄弱模块" class="advice-input"></el-input>
                    </div>
                </div>
            </div>


        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 报告表单数据
const reportForm = reactive({
    name: '', // 学员姓名
    sex: '', // 性别
    undergraduateSchool: '', // 本科院校
    undergraduateMajor: '', // 本科专业
    targetMajor: '', // 目标专业
    majorCode: '', // 专业代码
    phone: '', // 联系方式
    examYear: '', // 考研年份
    isMultiDisciplinary: '', // 跨专业
    educationalStyle: '', // 培养方式
    englishScore: '', // 高考英语成绩
    cet4: '', // 大学四级
    cet6: '', // 大学六级
    tofelScore: '', // 托福
    ieltsScore: '', // 雅思
    englishLevel: '', // 英语能力
    region: [], // 地区倾向
    intendedSchools: [], // 省份选择
    targetSchool: '', // 梦校
    targetSchoolName: '', // 梦校名称
    schoolLevel: '', // 院校层次
    referenceBooks: '', // 专业课指定参考书
    politics: '', // 政治成绩
    englishType: '', // 英语成绩
    mathType: '', // 数学成绩
    professional: '', // 专业课成绩
    totalScore: '', // 总分
    personalNeeds: '', // 个性化需求
    weakModules: '' // 薄弱模块
})
</script>

<style scoped lang="less">
.generate-container {
    background-color: #f5f5f5;
    min-height: 100vh;
}

.report-container {
    background: white;
    margin: 0 auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 报告封面头部样式 */
.report-header {
    width: 800px;
    height: 1122px;
    background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/report_pdf_front.png");
    background-repeat: no-repeat;
    background-size: contain;
}

/* 步骤内容样式保持原样 */
.steps {
    padding: 40px 60px;
}

.step-section {
    margin-bottom: 40px;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .report-header {
        height: auto;
        min-height: 400px;

        .header-content {
            flex-direction: column;
            text-align: center;
            padding: 30px 20px;

            .logo-section {
                margin-bottom: 20px;

                .header-logo {
                    height: 60px;
                }
            }

            .title-section {
                padding: 0;
                margin-bottom: 20px;

                .report-title {
                    font-size: 28px;
                }

                .report-subtitle {
                    font-size: 14px;
                }
            }

            .decoration-section {
                .decoration-image {
                    height: 80px;
                }
            }
        }
    }

    .steps {
        padding: 20px;
    }
}
</style>