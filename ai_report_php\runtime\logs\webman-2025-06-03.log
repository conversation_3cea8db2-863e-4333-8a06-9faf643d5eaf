[2025-06-03 08:50:49] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:50:49] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 08:50:49"} []
[2025-06-03 08:51:12] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:51:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:52:15] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:52:42] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:53:13] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:53:43] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:54:13] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:54:43] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:55:15] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:55:44] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:56:15] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:56:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 08:57:15] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":false,\"message\":\"登录将在后台执行，请稍后再次检查登录状态\",\"login_time\":null}"} []
[2025-06-03 09:51:39] default.INFO: 获取爬虫登录状态 {"res":false} []
[2025-06-03 09:52:09] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:52:40] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:53:10] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:53:40] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:54:11] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:54:40] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:55:10] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:55:41] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:56:13] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:56:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:57:13] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:57:43] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:58:17] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:58:45] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:59:13] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 09:59:43] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:00:14] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:00:44] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:01:14] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:01:45] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:02:15] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:02:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:03:18] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:03:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:04:16] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:04:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:05:17] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:05:47] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:06:17] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:06:48] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:07:19] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:07:48] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:08:18] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:08:49] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:09:18] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:09:49] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:10:19] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:10:51] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:11:21] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:11:50] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:12:21] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:12:50] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:13:20] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:13:51] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:14:21] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:14:51] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:15:22] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:15:52] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:16:21] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:16:54] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:17:26] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:17:53] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:18:23] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:18:53] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:19:23] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:19:54] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:20:24] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:20:54] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:21:25] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:21:55] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:22:21] default.INFO: 获取学生列表请求参数: {"name":"zhang","page":"1","limit":"10"} [] []
[2025-06-03 10:22:23] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-06-03 10:22:25] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:22:37] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"70","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"377","personalNeeds":"没有","weakModules":"","student_id":3} [] []
[2025-06-03 10:22:37] default.INFO: 四级成绩: 481 [] []
[2025-06-03 10:22:37] default.INFO: 六级成绩: 510 [] []
[2025-06-03 10:22:37] default.INFO: 托福成绩:  [] []
[2025-06-03 10:22:37] default.INFO: 英语能力: good [] []
[2025-06-03 10:22:37] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:22:38] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-06-03 10:22:38] default.INFO: 流式AI推荐请求参数: {"report_id":"354"} [] []
[2025-06-03 10:22:38] default.INFO: AI推荐学校请求参数: {"report_id":"354"} [] []
[2025-06-03 10:22:38] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-06-03 10:22:39] default.INFO: 梦想院校 [] []
[2025-06-03 10:22:39] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-06-03 10:22:39] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":64},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":86},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":25},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":98},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":116},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":116}] [] []
[2025-06-03 10:22:39] default.INFO: 不需要启动爬虫 [] []
[2025-06-03 10:22:39] default.INFO: 定时器执行 [] []
[2025-06-03 10:22:39] default.INFO: 流式 DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-06-03 10:22:39] default.INFO: 流式 DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校\n"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：70分\n数学（数学一）：110分\n专业课：120分\n总分：377分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：377分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线64分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线86分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线25分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线98分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线116分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线116分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。\n            为学生推荐最多10所不重复的最适合的学校，请严格在我给出院校列表中筛选 \n            对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于100字），以及高性价比院校 推荐原因（不少于100字）。\n            最后要求提供一所推荐综合性价比高的院校按以下格式输出 每输出一个完整的院校信息结尾加上l，\n            高性价比院校以h开头并且和最后一所推荐院校结尾的l和h之间不要加换行, 除了特定字母结尾之外\n            其他的输出的内容不要出现英文字母或者单词\n            以我要求的严格的格式返回结果。\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因\n            l\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因lh高性价比院校:学校名称,推荐原因\n\n            "}],"stream":true,"temperature":0.7,"max_tokens":4000} [] []
[2025-06-03 10:22:56] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:23:26] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:23:33] default.INFO: 流式响应结束:s中国矿业大学
m水利工程
d中国矿业大学作为211高校，水利工程专业实力较强，竞争较为激烈。该校地处江苏省，地理位置优越，吸引大量考生报考。根据往年录取情况，377分的预估成绩在该校属于中等偏上水平，但需注意专业课难度较高，实际录取分数可能比分数线高出20-30分。报考该校需要做好充分准备，特别是专业课复习要深入。
g建议重点复习水力学、水工建筑物等核心专业课内容，可参考历年真题把握命题规律。数学成绩需保持稳定在110分以上，英语和政治要争取更高分数以提升竞争力。可联系在读学长学姐获取内部资料和备考经验。
r该校水利工程专业学科评估排名靠前，师资力量雄厚，科研平台完善。毕业后在华东地区就业优势明显，且作为211高校在升学深造方面也有优势。l
s合肥工业大学
m水利工程
d合肥工业大学水利工程专业竞争相对温和，分数线较低。该校虽为211高校，但因地理位置等因素，报考热度适中。377分的预估成绩在该校具有明显优势，录取把握较大。但需注意该校专业课考查范围较广，涉及水文学、水资源等多方面内容。
g建议系统梳理水利工程各分支学科知识，建立完整知识体系。可适当降低数学复习强度，将更多时间投入专业课拓展复习。关注该校教授研究方向，在复试环节可能获得加分。英语可争取突破75分以增强竞争力。
r该校水利工程专业与当地水利部门合作密切，实践机会多。学费和生活成本较低，性价比高。毕业后在安徽及周边省份就业认可度高。l
s天津大学
m水利工程
d天津大学作为985高校，水利工程专业实力顶尖，竞争异常激烈。虽然分数线为352分，但实际录取分数通常在370分以上。377分的预估成绩处于录取边缘，需要各科成绩均衡且复试表现突出才有较大把握。
g必须全面提升各科成绩，政治和英语要争取80分以上，数学需达到120分。专业课要深入研究指定教材，并扩展阅读相关学术论文。建议提前联系导师，了解课题组研究方向。复试前需重点准备科研经历和专业英语。
r该校水利工程学科评估A+，师资和科研条件国内顶尖。毕业后发展前景广阔，特别适合有志于科研或高端技术岗位的考生。l
s江苏大学
m水利工程
d江苏大学水利工程专业竞争相对较小，分数线较低。该校虽非211，但在江苏省内认可度较高。377分的预估成绩远超分数线，录取把握很大。但需注意该校专业课命题有一定特色，需针对性准备。
g可将复习重点放在专业课上，确保拿到130分以上高分。数学可适当降低目标至100分，腾出时间强化英语和政治。建议收集整理该校近5年真题，把握命题重点和风格。复试前可重点关注江苏地区水利发展动态。
r该校水利工程专业实践性强，与江苏水利系统合作紧密。学费较低且奖学金覆盖率高，性价比突出。特别适合希望在江苏就业的考生。l
s浙江海洋大学
m水利工程
d浙江海洋大学水利工程专业竞争压力最小，分数线仅为261分。377分的预估成绩具有绝对优势，基本可以确保录取。但需注意该校专业方向偏向海洋水利，与传统水利工程有所区别。
g可将更多精力放在英语和政治上，争取两门课总分达到160分以上。专业课复习可侧重海岸工程、港口工程等特色内容。数学保持基本水平即可。建议提前了解导师研究方向，为复试做准备。
r该校在海洋水利领域特色鲜明，地理位置优越。录取门槛低但专业特色突出，特别适合对海洋工程感兴趣的考生。l
s上海海事大学
m水利工程
d上海海事大学水利工程专业竞争较小，分数线与浙江海洋大学相当。377分的成绩在该校优势明显，录取概率很高。该校专业方向侧重航运水利，与传统水利院校有所区别。
g建议专业课复习加入港口与航道工程等内容。英语可争取更高分数，因地处上海对英语要求较高。政治可参考上海地区评分标准进行针对性准备。数学保持现有水平即可，更多时间用于了解航运水利特色课程。
r该校地处上海，区位优势明显。虽非传统水利强校，但在航运水利领域独具特色。适合希望在上海发展的考生。lh高性价比院校:江苏大学,该校水利工程专业实力较强，在江苏省内认可度高，分数线适中但就业前景良好。377分的预估成绩在该校具有明显优势，录取把握大且能获得较好培养。地理位置优越，学费和生活成本合理，投入产出比高。特别适合希望在江苏及周边地区发展的考生。 [] []
[2025-06-03 10:23:57] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:24:27] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:25:01] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:25:33] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:25:48] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:25:48] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 10:25:48"} []
[2025-06-03 10:25:55] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:25:55] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 10:25:55"} []
[2025-06-03 10:28:15] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-06-03 10:28:32] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"70","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"377","personalNeeds":"没有","weakModules":"","student_id":3} [] []
[2025-06-03 10:28:32] default.INFO: 四级成绩: 481 [] []
[2025-06-03 10:28:32] default.INFO: 六级成绩: 510 [] []
[2025-06-03 10:28:32] default.INFO: 托福成绩:  [] []
[2025-06-03 10:28:32] default.INFO: 英语能力: good [] []
[2025-06-03 10:28:32] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:28:33] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-06-03 10:28:53] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u73ca\u73ca","page":"1","limit":"10"} [] []
[2025-06-03 10:29:08] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊珊","sex":"2","phone":"13966311241","undergraduateSchool":"河海大学","undergraduateSchoolName":"","undergraduateMajor":"水利水电工程","undergraduateMajorName":"","targetMajor":"(081500)水利工程","targetMajorName":"(081500)水利工程","majorCode":"081500","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,上海市,江苏省,浙江省,安徽省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"无","politics":"77","englishType":"英语一","englishS":"70","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"377","personalNeeds":"没有","weakModules":"","student_id":3} [] []
[2025-06-03 10:29:08] default.INFO: 四级成绩: 481 [] []
[2025-06-03 10:29:08] default.INFO: 六级成绩: 510 [] []
[2025-06-03 10:29:08] default.INFO: 托福成绩:  [] []
[2025-06-03 10:29:08] default.INFO: 英语能力: good [] []
[2025-06-03 10:29:08] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:29:09] default.INFO: 流式AI推荐请求参数: {"report_id":"356"} [] []
[2025-06-03 10:29:09] default.INFO: AI推荐学校请求参数: {"report_id":"356"} [] []
[2025-06-03 10:29:10] default.INFO: 获取数据库中匹配的院校["18873","18933","26523","26524","81426","81436","18873","18933","26523","26524","18874","23154","23155","84690","84700"] [] []
[2025-06-03 10:29:10] default.INFO: 梦想院校 [] []
[2025-06-03 10:29:10] default.INFO: 梦想院校{"id":"18873","province":"\u6c5f\u82cf\u7701","xueke_name":"(08)\u5de5\u5b66","first_level_code":"0815","second_level_name":"(081500)\u6c34\u5229\u5de5\u7a0b","school_name":"\u4e2d\u56fd\u77ff\u4e1a\u5927\u5b66","major_code":"081500","major_name":"081500\u6c34\u5229\u5de5\u7a0b","area":"\u6c5f\u82cf\u7701","school_type":"211,\u53cc\u4e00\u6d41","college":"\u8d44\u6e90\u4e0e\u5730\u7403\u79d1\u5b66\u5b66..","study_type":"\u5168\u65e5\u5236","warning_level":"5:11","ranking":"58:64","admission":"\u603b\u5f55:4  \u4e00\u5fd7\u613f:1   \u8c03\u5242:3  \u5fc5\u8fbe\u5206:313","course_suggestion":"\u653f:73  \u672a   \u82f1:76  \u672a   \u4e13\u4e00:74  \u96be   \u4e13\u4e8c:135  \u672a","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=8CjN6zUuFPibsSc9XYjNfFMJHmn2PB","detail_url_status":1,"created_at":"2025-04-29 23:58:12","must_reach_score":313,"crawl_start_time":"2025-05-26 14:39:39","crawl_end_time":"2025-05-26 14:39:39"} [] []
[2025-06-03 10:29:10] default.INFO: 学校列表: [{"school_id":"18873","school_name":"中国矿业大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":313,"score_diff":64},{"school_id":"26523","school_name":"合肥工业大学","province":"安徽省","area":"安徽省","major_name":"081500水利工程","school_type":"211,双一流","must_reach_score":291,"score_diff":86},{"school_id":"81426","school_name":"天津大学","province":"天津市","area":"天津市","major_name":"081500水利工程","school_type":"985,211,双一流","must_reach_score":352,"score_diff":25},{"school_id":"18874","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"081500水利工程","school_type":"","must_reach_score":279,"score_diff":98},{"school_id":"23154","school_name":"浙江海洋大学","province":"浙江省","area":"浙江省","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":116},{"school_id":"84690","school_name":"上海海事大学","province":"上海市","area":"上海市","major_name":"081500水利工程","school_type":"","must_reach_score":261,"score_diff":116}] [] []
[2025-06-03 10:29:10] default.INFO: 不需要启动爬虫 [] []
[2025-06-03 10:29:10] default.INFO: 获取国家线数据请求参数: {"major_code":"081500"} [] []
[2025-06-03 10:29:10] default.INFO: 定时器执行 [] []
[2025-06-03 10:29:10] default.INFO: 流式 DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-06-03 10:29:10] default.INFO: 流式 DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校\n"},{"role":"user","content":"学生基本信息：\n姓名：张珊珊\n性别：女\n本科院校：河海大学\n本科专业：水利水电工程\n目标专业：(081500)水利工程（专业代码：081500）\n是否跨专业：否\n\n考试成绩预估：\n政治：77分\n英语（英语一）：70分\n数学（数学一）：110分\n专业课：120分\n总分：377分\n\n目标偏好：\n目标区域：A区\n目标省份：天津市,上海市,江苏省,浙江省,安徽省\n院校层次：211\n梦想院校：中国矿业大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：081500\n- 预估总分：377分\n- 目标区域：A区\n- 目标省份：天津市,上海市,江苏省,浙江省,安徽省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 中国矿业大学（江苏省）- 081500水利工程，分数线：313分，高出分数线64分\n2. 合肥工业大学（安徽省）- 081500水利工程，分数线：291分，高出分数线86分\n\n【985,211,双一流类院校】\n1. 天津大学（天津市）- 081500水利工程，分数线：352分，高出分数线25分\n\n【类院校】\n1. 江苏大学（江苏省）- 081500水利工程，分数线：279分，高出分数线98分\n2. 浙江海洋大学（浙江省）- 081500水利工程，分数线：261分，高出分数线116分\n3. 上海海事大学（上海市）- 081500水利工程，分数线：261分，高出分数线116分\n\n【按省份分类】\n江苏省：中国矿业大学、江苏大学\n安徽省：合肥工业大学\n天津市：天津大学\n浙江省：浙江海洋大学\n上海市：上海海事大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。\n            为学生推荐最多10所不重复的最适合的学校，请严格在我给出院校列表中筛选 \n            对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于100字），以及高性价比院校 推荐原因（不少于100字）。\n            最后要求提供一所推荐综合性价比高的院校按以下格式输出 每输出一个完整的院校信息结尾加上l，\n            高性价比院校以h开头并且和最后一所推荐院校结尾的l和h之间不要加换行, 除了特定字母结尾之外\n            其他的输出的内容不要出现英文字母或者单词\n            以我要求的严格的格式返回结果。\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因\n            l\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因lh高性价比院校:学校名称,推荐原因\n\n            "}],"stream":true,"temperature":0.7,"max_tokens":4000} [] []
[2025-06-03 10:30:00] default.INFO: 流式响应结束:s中国矿业大学
m水利工程
d中国矿业大学作为211高校，水利工程专业实力较强，在江苏省内具有较高声誉。该校水利工程专业竞争较为激烈，历年录取分数线在313分左右，377分的预估成绩高出64分，具有较大优势。但需注意该校对专业课要求较高，复试中可能侧重考察专业实践能力。
g建议重点复习水力学、水工建筑物等核心专业课内容，同时关注近年水利工程热点问题。保持数学成绩稳定在110分以上，英语可争取突破75分以增强竞争力。复试前建议联系导师并准备相关科研项目经历。
r该校水利工程专业学科评估排名靠前，且地处江苏省就业资源丰富。作为211院校，学历认可度高，且预估分数优势明显，是稳妥且有发展前景的选择。
l
s合肥工业大学
m水利工程
d合肥工业大学水利工程专业分数线为291分，377分预估成绩优势显著。该校水利工程在安徽省内首屈一指，但相比东部沿海地区211院校竞争压力较小。需要注意的是该校近年报考热度有所上升，复试淘汰率约20%。
g建议重点巩固数学基础，争取将数学成绩提升至120分以上。专业课可参考历年真题进行针对性复习。英语可保持现有水平，政治争取突破80分以拉开分差。复试前建议了解安徽地区水利发展现状。
r该校水利工程专业实力雄厚且分数要求较低，377分具有绝对优势。作为安徽省重点211高校，就业前景良好且生活成本较低，性价比极高。
l
s天津大学
m水利工程
d天津大学作为985高校，水利工程专业全国排名靠前，竞争非常激烈。虽然分数线为352分，但实际录取分数通常更高。377分预估成绩优势不大，需要面临与众多高分考生竞争的压力。
g必须将数学成绩提升至120分以上，专业课争取130分以上。英语和政治都要力争高分，建议目标为英语75+、政治80+。需提前联系导师并准备高质量的科研材料，复试要突出个人优势。
r虽然竞争激烈，但天津大学水利工程专业含金量极高，毕业后发展平台广阔。对于有实力的考生值得冲刺，但要做好调剂准备。
l
s江苏大学
m水利工程
d江苏大学水利工程专业分数线为279分，377分具有绝对优势。该校虽非211，但在江苏省内认可度较高，竞争压力相对较小。报考考生多来自省内普通高校，生源质量较为平均。
g可适当降低备考强度，保持现有成绩水平即可。重点准备复试中的实践能力考核，多关注江苏地区水利工程案例。英语和政治可维持现状，把更多时间放在专业实践上。
r分数优势极大且就业地域性强，特别适合想在江苏省发展的考生。学校专业实力不俗且竞争压力小，是保底的好选择。
l
s浙江海洋大学
m水利工程
d浙江海洋大学水利工程专业分数线仅261分，377分超出116分，优势极为明显。该校专业特色偏向海洋水利方向，竞争压力很小，基本过线即可录取。
g可以轻松备考，重点了解海洋水利特色课程。建议利用分数优势争取一等奖学金，同时可提前联系心仪导师。保持现有各科成绩即可。
r分数要求极低且具有鲜明专业特色，特别适合对海洋水利感兴趣的考生。在浙江省内就业也有一定优势。
l
s上海海事大学
m水利工程
d上海海事大学水利工程分数线同样为261分，377分优势巨大。该校专业方向偏向港口与航道工程，竞争较小但地域优势明显。报考考生多为看重上海区位优势。
g保持现有成绩即可轻松过线，建议多关注上海地区水利建设项目。英语可争取更高分以突显优势。复试前建议准备与港口工程相关的知识。
r上海地域优势显著，虽然学校专业排名一般，但实习就业机会多。特别适合想在上海发展的考生。
lh高性价比院校:合肥工业大学,该校是211高校中水利工程专业实力与考取难度最佳平衡的选择。377分远超分数线，录取把握大；作为安徽省重点高校，区域内就业优势明显；相比同层次院校竞争压力较小，投入产出比极高。 [] []
[2025-06-03 10:30:01] default.INFO: 找到学校数据: 浙江海洋大学, ID: 389, logo:  [] []
[2025-06-03 10:30:01] default.INFO: 找到学校数据: 江苏大学, ID: 122, logo:  [] []
[2025-06-03 10:30:02] default.INFO: 找到学校数据: 天津大学, ID: 18, logo:  [] []
[2025-06-03 10:30:02] default.INFO: 找到学校数据: 合肥工业大学, ID: 71, logo:  [] []
[2025-06-03 10:30:03] default.INFO: 找到学校数据: 中国矿业大学, ID: 113, logo:  [] []
[2025-06-03 10:30:03] default.INFO: 找到学校数据: 上海海事大学, ID: 259, logo:  [] []
[2025-06-03 10:30:05] default.INFO: 报告数据保存成功，报告ID: 356，共插入 7 条记录 [] []
[2025-06-03 10:36:00] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9523","undergraduateMajorName":"哲学","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"average","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"985","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 10:36:00] default.INFO: 四级成绩: 440 [] []
[2025-06-03 10:36:00] default.INFO: 六级成绩: 460 [] []
[2025-06-03 10:36:00] default.INFO: 托福成绩:  [] []
[2025-06-03 10:36:00] default.INFO: 英语能力: average [] []
[2025-06-03 10:36:00] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:36:00] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 10:36:21] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9523","undergraduateMajorName":"哲学","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"average","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"36","targetSchoolName":"北京师范大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 10:36:21] default.INFO: 四级成绩: 440 [] []
[2025-06-03 10:36:21] default.INFO: 六级成绩: 460 [] []
[2025-06-03 10:36:21] default.INFO: 托福成绩:  [] []
[2025-06-03 10:36:21] default.INFO: 英语能力: average [] []
[2025-06-03 10:36:21] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:36:22] default.INFO: 获取国家线数据请求参数: {"major_code":"010100"} [] []
[2025-06-03 10:36:22] default.INFO: 流式AI推荐请求参数: {"report_id":"357"} [] []
[2025-06-03 10:36:22] default.INFO: AI推荐学校请求参数: {"report_id":"357"} [] []
[2025-06-03 10:36:23] default.INFO: 获取数据库中匹配的院校["21645","21669","21646","21670","21645","21669","21646","21670","21647","21648","21671"] [] []
[2025-06-03 10:36:24] default.INFO: 梦想院校 [] []
[2025-06-03 10:36:24] default.INFO: 梦想院校{"id":"80513","province":"\u5317\u4eac\u5e02","xueke_name":"(01)\u54f2\u5b66","first_level_code":"0101","second_level_name":"(010100)\u54f2\u5b66","school_name":"\u5317\u4eac\u5e08\u8303\u5927\u5b66","major_code":"010100","major_name":"010100\u54f2\u5b66","area":"\u5317\u4eac\u5e02","school_type":"985,211,\u53cc\u4e00\u6d41","college":"\u54f2\u5b66\u5b66\u9662","study_type":"\u5168\u65e5\u5236","warning_level":"13:5","ranking":"7:138","admission":"\u603b\u5f55:43  \u4e00\u5fd7\u613f:43   \u8c03\u5242:0  \u5fc5\u8fbe\u5206:365","course_suggestion":"\u653f:64  \u4e2d   \u82f1:70  \u96be   \u4e13\u4e00:129  \u96be   \u4e13\u4e8c:126  \u7b80","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=ZwsG9tivK4dM1MjR5RyiHlYcUilRMQ","detail_url_status":0,"created_at":"2025-05-09 04:54:05","must_reach_score":365,"crawl_start_time":null,"crawl_end_time":null} [] []
[2025-06-03 10:36:24] default.INFO: 学校列表: [{"school_id":"21645","school_name":"南京农业大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"211,双一流","must_reach_score":368,"score_diff":27},{"school_id":"21646","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"211,双一流","must_reach_score":362,"score_diff":33},{"school_id":"21647","school_name":"苏州科技大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"","must_reach_score":331,"score_diff":64},{"school_id":"21648","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"","must_reach_score":331,"score_diff":64},{"school_id":"80513","school_name":"北京师范大学","province":"北京市","area":"北京市","major_name":"010100哲学","school_type":"985,211,双一流","must_reach_score":365,"score_diff":30}] [] []
[2025-06-03 10:36:26] default.INFO: 启动爬虫响应: {"task_id":"052c74de-7f33-460b-88ba-fd16b6d39b55","status":"running","message":"任务已创建，正在后台处理","total":5} [] []
[2025-06-03 10:36:26] default.INFO: 定时器执行 [] []
[2025-06-03 10:36:26] default.INFO: 流式 DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-06-03 10:36:26] default.INFO: 流式 DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校\n"},{"role":"user","content":"学生基本信息：\n姓名：王五\n性别：男\n本科院校：安徽大学\n本科专业：哲学\n目标专业：(010100)哲学（专业代码：010100）\n是否跨专业：否\n\n考试成绩预估：\n政治：70分\n英语（英语二）：75分\n数学（数学二）：120分\n专业课：130分\n总分：395分\n\n目标偏好：\n目标区域：A区\n目标省份：江苏省\n院校层次：211\n梦想院校：北京师范大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：010100\n- 预估总分：395分\n- 目标区域：A区\n- 目标省份：江苏省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 南京农业大学（江苏省）- 010100哲学，分数线：368分，高出分数线27分\n2. 苏州大学（江苏省）- 010100哲学，分数线：362分，高出分数线33分\n\n【类院校】\n1. 苏州科技大学（江苏省）- 010100哲学，分数线：331分，高出分数线64分\n2. 江苏大学（江苏省）- 010100哲学，分数线：331分，高出分数线64分\n\n【985,211,双一流类院校】\n1. 北京师范大学（北京市）- 010100哲学，分数线：365分，高出分数线30分\n\n【按省份分类】\n江苏省：南京农业大学、苏州大学、苏州科技大学、江苏大学\n北京市：北京师范大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。\n            为学生推荐最多10所不重复的最适合的学校，请严格在我给出院校列表中筛选 \n            对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于100字），以及高性价比院校 推荐原因（不少于100字）。\n            最后要求提供一所推荐综合性价比高的院校按以下格式输出 每输出一个完整的院校信息结尾加上l，\n            高性价比院校以h开头并且和最后一所推荐院校结尾的l和h之间不要加换行, 除了特定字母结尾之外\n            其他的输出的内容不要出现英文字母或者单词\n            以我要求的严格的格式返回结果。\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因\n            l\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因lh高性价比院校:学校名称,推荐原因\n\n            "}],"stream":true,"temperature":0.7,"max_tokens":4000} [] []
[2025-06-03 10:37:00] default.INFO: 流式响应结束:s南京农业大学  
m哲学  
d南京农业大学作为江苏省内211高校，哲学专业竞争相对温和。该校哲学学科评估为B-，在农业院校中特色鲜明，报考人数中等。395分的预估成绩超出往年分数线27分，录取把握较大，但需注意专业课可能存在压分现象。  
g建议重点巩固西方哲学史和马克思主义哲学基础，关注农业伦理学等特色方向。英语需保持75+水平，政治可冲刺75分以提升优势。专业课需深入研究历年真题，把握南农出题偏好。  
r该校哲学专业性价比高，211平台资源丰富，且分数线相对友好。地理位置优越，适合想在江苏发展的考生。  
l  
s苏州大学  
m哲学  
d苏州大学哲学学科评估B，在211院校中排名靠前，报考热度较高。395分超出分数线33分较为安全，但需警惕苏大近年分数线波动。复试比例约1:1.2，需重视面试表现。  
g建议强化中国哲学原著阅读能力，苏大侧重传统哲学研究。英语二需保持优势，数学可适当降低投入。专业课建议精读《中国哲学史》教材，关注江南儒学特色方向。  
r苏州大学哲学底蕴深厚，地域优势明显，毕业生在长三角认可度高，适合追求学术与就业平衡的考生。  
l  
s北京师范大学  
m哲学  
d北师大哲学学科评估A+，全国顶尖水平，竞争异常激烈。虽然395分超线30分，但实际录取常需400+。报考者多为985高校生源，复试对学术潜力要求极高。  
g必须将专业课目标定为140+，政治英语需双75+。建议研读北师大导师近三年论文，提前联系导师。备考期间需完成1-2篇哲学小论文作为学术能力证明。  
r北师大是哲学学科殿堂级学府，适合有明确学术志向的考生。虽难度大，但平台资源无可替代。  
l  
s苏州科技大学  
m哲学  
d该校哲学专业竞争压力小，过国家线即可录取。生源以省内二本院校为主，395分具有绝对优势。但需注意学科实力较弱，导师资源有限。  
g可将更多精力放在公共课上，争取政治80+、英语80+打造亮点。专业课保持基础即可，重点准备复试中的实践能力考核。  
r适合求稳考生，录取概率极高，且苏州地域发展前景好，适合以学历为跳板的考生。  
lh高性价比院校:南京农业大学,该校兼具211院校平台优势与相对温和的竞争环境，哲学专业特色方向明确，分数线与考生成绩匹配度高，地域就业资源丰富，投入产出比最优。 [] []
[2025-06-03 10:37:00] default.INFO: 找到学校数据: 苏州科技大学, ID: 482, logo:  [] []
[2025-06-03 10:37:00] default.INFO: 找到学校数据: 北京师范大学, ID: 36, logo:  [] []
[2025-06-03 10:37:01] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-06-03 10:37:01] default.INFO: 找到学校数据: 南京农业大学, ID: 58, logo:  [] []
[2025-06-03 10:37:02] default.INFO: 报告数据保存成功，报告ID: 357，共插入 5 条记录 [] []
[2025-06-03 10:43:55] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:43:55] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 10:43:55"} []
[2025-06-03 10:44:09] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 10:44:09] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 10:44:09"} []
[2025-06-03 10:44:21] default.INFO: 获取学生列表请求参数: {"name":"\u674e\u56db","page":"1","limit":"10"} [] []
[2025-06-03 10:44:28] default.INFO: 获取学生列表请求参数: {"name":"\u738b\u4e94","page":"1","limit":"10"} [] []
[2025-06-03 10:45:01] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"哲学","undergraduateMajorName":"","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"良好","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"6","targetSchoolName":"复旦大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":"","student_id":8} [] []
[2025-06-03 10:45:02] default.INFO: 四级成绩: 440 [] []
[2025-06-03 10:45:02] default.INFO: 六级成绩: 460 [] []
[2025-06-03 10:45:02] default.INFO: 托福成绩:  [] []
[2025-06-03 10:45:02] default.INFO: 英语能力: 良好 [] []
[2025-06-03 10:45:02] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:45:02] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 10:45:14] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":"安徽大学","undergraduateSchoolName":"","undergraduateMajor":"哲学","undergraduateMajorName":"","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"良好","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"8","targetSchoolName":"浙江大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":"","student_id":8} [] []
[2025-06-03 10:45:14] default.INFO: 四级成绩: 440 [] []
[2025-06-03 10:45:14] default.INFO: 六级成绩: 460 [] []
[2025-06-03 10:45:14] default.INFO: 托福成绩:  [] []
[2025-06-03 10:45:14] default.INFO: 英语能力: 良好 [] []
[2025-06-03 10:45:14] default.INFO: 地区倾向: A区 [] []
[2025-06-03 10:45:15] default.INFO: 流式AI推荐请求参数: {"report_id":"358"} [] []
[2025-06-03 10:45:15] default.INFO: AI推荐学校请求参数: {"report_id":"358"} [] []
[2025-06-03 10:45:15] default.INFO: 获取数据库中匹配的院校["21645","21669","21646","21670","21645","21669","21646","21670","21647","21648","21671"] [] []
[2025-06-03 10:45:16] default.INFO: 梦想院校 [] []
[2025-06-03 10:45:16] default.INFO: 梦想院校{"id":"24771","province":"\u6d59\u6c5f\u7701","xueke_name":"(01)\u54f2\u5b66","first_level_code":"0101","second_level_name":"(010100)\u54f2\u5b66","school_name":"\u6d59\u6c5f\u5927\u5b66","major_code":"010100","major_name":"010100\u54f2\u5b66","area":"\u6d59\u6c5f\u7701","school_type":"985,211,\u53cc\u4e00\u6d41","college":"\u54f2\u5b66\u5b66\u9662","study_type":"\u5168\u65e5\u5236","warning_level":"\u672a\u77e5:8","ranking":"10:138","admission":"24\u5e74\u54f2\u5b66\u5b66\u9662\u5f55\u53d6\u60c5\u51b5","course_suggestion":"24\u5e74\u54f2\u5b66\u5b66\u9662\u5f55\u53d6\u60c5\u51b5","detail_url":"http:\/\/www.zhiliaojy.com\/admin\/zxxiangqing.aspx?dataid=YLyyEtW7Wp1y7Vf11H6OHpov5FpXjp","detail_url_status":0,"created_at":"2025-04-30 15:07:33","must_reach_score":null,"crawl_start_time":null,"crawl_end_time":null} [] []
[2025-06-03 10:45:16] default.INFO: 学校列表: [{"school_id":"21645","school_name":"南京农业大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"211,双一流","must_reach_score":368,"score_diff":27},{"school_id":"21646","school_name":"苏州大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"211,双一流","must_reach_score":362,"score_diff":33},{"school_id":"21647","school_name":"苏州科技大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"","must_reach_score":331,"score_diff":64},{"school_id":"21648","school_name":"江苏大学","province":"江苏省","area":"江苏省","major_name":"010100哲学","school_type":"","must_reach_score":331,"score_diff":64},{"school_id":"24771","school_name":"浙江大学","province":"浙江省","area":"浙江省","major_name":"010100哲学","school_type":"985,211,双一流","must_reach_score":null,"score_diff":395}] [] []
[2025-06-03 10:45:16] default.INFO: 启动爬虫响应: {"task_id":"08c7fb34-0419-4aee-8f9e-37c95ae3e9e0","status":"running","message":"任务已创建，正在后台处理","total":5} [] []
[2025-06-03 10:45:16] default.INFO: 获取国家线数据请求参数: {"major_code":"010100"} [] []
[2025-06-03 10:45:16] default.INFO: 定时器执行 [] []
[2025-06-03 10:45:16] default.INFO: 流式 DeepSeek API 请求URL: https://api.deepseek.com/chat/completions [] []
[2025-06-03 10:45:16] default.INFO: 流式 DeepSeek API 请求负载: {"model":"deepseek-chat","messages":[{"role":"system","content":"你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校\n"},{"role":"user","content":"学生基本信息：\n姓名：王五\n性别：男\n本科院校：安徽大学\n本科专业：哲学\n目标专业：(010100)哲学（专业代码：010100）\n是否跨专业：否\n\n考试成绩预估：\n政治：70分\n英语（英语二）：75分\n数学（数学二）：120分\n专业课：130分\n总分：395分\n\n目标偏好：\n目标区域：A区\n目标省份：江苏省\n院校层次：211\n梦想院校：浙江大学\n个性化需求：没有\n\n搜索匹配条件：\n- 目标专业代码：010100\n- 预估总分：395分\n- 目标区域：A区\n- 目标省份：江苏省\n- 院校层次：211,双一流\n\n根据学生的目标专业、预估分数、所选区域、省份信息和院校层次，系统筛选出以下可能适合的学校：\n\n【211,双一流类院校】\n1. 南京农业大学（江苏省）- 010100哲学，分数线：368分，高出分数线27分\n2. 苏州大学（江苏省）- 010100哲学，分数线：362分，高出分数线33分\n\n【类院校】\n1. 苏州科技大学（江苏省）- 010100哲学，分数线：331分，高出分数线64分\n2. 江苏大学（江苏省）- 010100哲学，分数线：331分，高出分数线64分\n\n【985,211,双一流类院校】\n1. 浙江大学（浙江省）- 010100哲学，分数线：分，高出分数线395分\n\n【按省份分类】\n江苏省：南京农业大学、苏州大学、苏州科技大学、江苏大学\n浙江省：浙江大学\n你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，其中提供的分数是必达分不是录取分数。\n            为学生推荐最多10所不重复的最适合的学校，请严格在我给出院校列表中筛选 \n            对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于100字），以及高性价比院校 推荐原因（不少于100字）。\n            最后要求提供一所推荐综合性价比高的院校按以下格式输出 每输出一个完整的院校信息结尾加上l，\n            高性价比院校以h开头并且和最后一所推荐院校结尾的l和h之间不要加换行, 除了特定字母结尾之外\n            其他的输出的内容不要出现英文字母或者单词\n            以我要求的严格的格式返回结果。\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因\n            l\n            s学校名称\n            m专业名称\n            d竞争难度分析\n            g备考目标建议\n            r推荐原因lh高性价比院校:学校名称,推荐原因\n\n            "}],"stream":true,"temperature":0.7,"max_tokens":4000} [] []
[2025-06-03 10:45:50] default.INFO: 流式响应结束:s南京农业大学  
m010100哲学  
d南京农业大学作为江苏省内211高校，哲学专业竞争相对温和。该校哲学学科评估为B-，在农业伦理、生态哲学等方向有特色优势。近年报录比约5:1，复试线稳定在国家线以上10-15分。考生需注意其专业课侧重中西哲学史与马克思主义哲学原理的交叉考查。  
g建议重点强化《西方哲学史》和《中国哲学史》的脉络梳理，关注农业伦理相关前沿论文。政治科目需确保70+，英语要突破75分以拉开差距。专业课需针对南农历年真题进行专题训练，尤其注重论述题的逻辑构建能力。  
r该校哲学专业与农林学科交叉特色明显，对395分考生性价比较高。地处南京学术资源丰富，211平台有利于未来深造或就业，且分数线与考生预估分匹配度达93%。  
l  
s苏州大学  
m010100哲学  
d苏州大学哲学系为江苏省重点学科，评估B档，在科技哲学、美学领域实力突出。近年报考热度上升明显，报录比约7:1，复试线常超国家线20分左右。专业课命题侧重苏大学派研究特色，对原著解读能力要求较高。  
g需精读《德意志意识形态》等苏大指定参考书，建立哲学问题意识。英语要争取80分以上，数学保持120分优势。建议提前联系导师了解研究动态，专业课复习需结合《苏州大学哲学评论》期刊内容。  
r该校哲学学科发展迅猛，国际交流机会多。预估分超出历年线30+分，录取把握大。苏州地域优势明显，211院校资源对哲学专业就业有显著加成。  
l  
s浙江大学  
m010100哲学  
d浙江大学哲学系为985顶尖学科，评估A-档，现象学、分析哲学方向全国领先。报录比常达15:1，实际录取分多超400分。专业课考查深度广度兼备，近年增加外文文献分析题型，对跨学科能力要求极高。  
g必须系统研读胡塞尔、海德格尔原著，数学需冲刺130+弥补英语劣势。建议参加浙大哲学夏令营获取优选资格，专业课要准备10万字以上的读书笔记。政治英语需双75+才具竞争力。  
r虽考生分数距往年录取线有差距，但数学优势明显。若冲刺成功可获得顶级学术平台，建议作为梦想院校尝试。  
lh高性价比院校:南京农业大学,该校哲学专业在211院校中特色鲜明，分数线与考生匹配度最高。农业伦理方向就业前景好，南京地域优势明显且竞争压力小于苏大，395分可稳居录取前列。 [] []
[2025-06-03 10:45:51] default.INFO: 找到学校数据: 苏州大学, ID: 86, logo:  [] []
[2025-06-03 10:45:51] default.INFO: 找到学校数据: 南京农业大学, ID: 58, logo:  [] []
[2025-06-03 10:45:51] default.INFO: 找到学校数据: 浙江大学, ID: 8, logo: yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/8.jpeg [] []
[2025-06-03 10:45:53] default.INFO: 报告数据保存成功，报告ID: 358，共插入 4 条记录 [] []
[2025-06-03 10:50:09] default.INFO: 获取学生列表请求参数: {"name":"\u674e\u56db","page":"1","limit":"10"} [] []
[2025-06-03 10:55:53] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-06-03 10:55:57] default.INFO: 获取学生列表请求参数: {"name":"\u738b","page":"1","limit":"10"} [] []
[2025-06-03 10:56:27] default.INFO: 获取学生列表请求参数: {"name":"\u738b","page":"1","limit":"10"} [] []
[2025-06-03 11:02:03] default.INFO: 获取学生列表请求参数: {"name":"\u738b\u4e94","page":"1","limit":"10"} [] []
[2025-06-03 11:02:46] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":167,"undergraduateSchoolName":"","undergraduateMajor":9523,"undergraduateMajorName":"","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"良好","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"8","targetSchoolName":"浙江大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":"","student_id":8} [] []
[2025-06-03 11:02:47] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:02:47] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:02:47] default.INFO: 托福成绩:  [] []
[2025-06-03 11:02:47] default.INFO: 英语能力: 良好 [] []
[2025-06-03 11:02:47] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:14:38] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":167,"undergraduateSchoolName":"安徽大学","undergraduateMajor":9523,"undergraduateMajorName":"哲学","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"良好","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"113","targetSchoolName":"中国矿业大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":"","student_id":8} [] []
[2025-06-03 11:14:38] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:14:38] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:14:38] default.INFO: 托福成绩:  [] []
[2025-06-03 11:14:38] default.INFO: 英语能力: 良好 [] []
[2025-06-03 11:14:38] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:14:38] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:14:49] default.INFO: toggleAIOverlay 请求参数: {"name":"王五","sex":"1","phone":"18756945954","undergraduateSchool":167,"undergraduateSchoolName":"安徽大学","undergraduateMajor":9523,"undergraduateMajorName":"哲学","targetMajor":"(010100)哲学","targetMajorName":"(010100)哲学","majorCode":"010100","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"undergraduateTranscript":[{"id":1,"title":"英语","score":60},{"id":2,"title":"哲学基础","score":80}],"englishScore":"120","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"良好","targetRegion":"A区","region":"A区","targetProvinces":"江苏省","targetSchool":"8","targetSchoolName":"浙江大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语二","englishS":"75","mathType":"数学二","mathScore":"120","professionalScore":"130","totalScore":"395","personalNeeds":"没有","weakModules":"","student_id":8} [] []
[2025-06-03 11:14:50] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:14:50] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:14:50] default.INFO: 托福成绩:  [] []
[2025-06-03 11:14:50] default.INFO: 英语能力: 良好 [] []
[2025-06-03 11:14:50] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:17:50] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生","sex":"2","phone":"13966311242","undergraduateSchool":"71","undergraduateSchoolName":"合肥工业大学","undergraduateMajor":"3643","undergraduateMajorName":"","targetMajor":"(080201)机械制造及其自动化","targetMajorName":"(080201)机械制造及其自动化","majorCode":"080201","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"englishScore":"135","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"6","targetSchoolName":"复旦大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:17:50] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:17:50] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:17:50] default.INFO: 托福成绩:  [] []
[2025-06-03 11:17:50] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:17:50] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:17:50] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:18:06] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生","sex":"2","phone":"13966311242","undergraduateSchool":"71","undergraduateSchoolName":"合肥工业大学","undergraduateMajor":"3643","undergraduateMajorName":"","targetMajor":"(080201)机械制造及其自动化","targetMajorName":"(080201)机械制造及其自动化","majorCode":"080201","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"englishScore":"135","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"26","targetSchoolName":"哈尔滨工业大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:18:06] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:18:06] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:18:06] default.INFO: 托福成绩:  [] []
[2025-06-03 11:18:06] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:18:06] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:18:06] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:18:32] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生","sex":"2","phone":"13966311242","undergraduateSchool":"71","undergraduateSchoolName":"合肥工业大学","undergraduateMajor":"3650","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":85}],"englishScore":"135","cet4":"440","cet6":"460","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"没有","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:18:32] default.INFO: 四级成绩: 440 [] []
[2025-06-03 11:18:32] default.INFO: 六级成绩: 460 [] []
[2025-06-03 11:18:32] default.INFO: 托福成绩:  [] []
[2025-06-03 11:18:32] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:18:32] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:22:14] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9477","undergraduateMajorName":"","targetMajor":"(070104)应用数学","targetMajorName":"(070104)应用数学","majorCode":"070104","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"71","targetSchoolName":"合肥工业大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:22:14] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:22:14] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:22:14] default.INFO: 托福成绩:  [] []
[2025-06-03 11:22:14] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:22:14] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:22:14] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:22:31] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9477","undergraduateMajorName":"","targetMajor":"(070104)应用数学","targetMajorName":"(070104)应用数学","majorCode":"070104","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"10","targetSchoolName":"南京大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:22:31] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:22:31] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:22:31] default.INFO: 托福成绩:  [] []
[2025-06-03 11:22:31] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:22:31] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:22:31] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:22:41] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9477","undergraduateMajorName":"","targetMajor":"(070104)应用数学","targetMajorName":"(070104)应用数学","majorCode":"070104","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"63","targetSchoolName":"南京理工大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:22:41] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:22:41] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:22:41] default.INFO: 托福成绩:  [] []
[2025-06-03 11:22:41] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:22:41] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:22:41] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:22:53] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9477","undergraduateMajorName":"","targetMajor":"(070104)应用数学","targetMajorName":"(070104)应用数学","majorCode":"070104","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"6","targetSchoolName":"复旦大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:22:53] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:22:53] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:22:53] default.INFO: 托福成绩:  [] []
[2025-06-03 11:22:53] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:22:53] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:22:53] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:23:21] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9541","undergraduateMajorName":"","targetMajor":"(050201)英语语言文学","targetMajorName":"(050201)英语语言文学","majorCode":"050201","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"6","targetSchoolName":"复旦大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:23:21] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:23:21] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:23:21] default.INFO: 托福成绩:  [] []
[2025-06-03 11:23:21] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:23:21] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:23:22] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:23:40] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9493","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"6","targetSchoolName":"复旦大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:23:40] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:23:40] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:23:40] default.INFO: 托福成绩:  [] []
[2025-06-03 11:23:40] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:23:40] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:23:40] default.ERROR: toggleAIOverlay 异常: 梦校没有改专业研究生招生计划 in G:\code\php\webman\ai_report_php\app\controller\StudentController.php on line 989 [] []
[2025-06-03 11:23:55] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生1","sex":"1","phone":"13083409462","undergraduateSchool":"167","undergraduateSchoolName":"安徽大学","undergraduateMajor":"9493","undergraduateMajorName":"","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":90}],"englishScore":"141","cet4":"520","cet6":"540","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"75","mathType":"数学一","mathScore":"110","professionalScore":"120","totalScore":"375","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:23:55] default.INFO: 四级成绩: 520 [] []
[2025-06-03 11:23:55] default.INFO: 六级成绩: 540 [] []
[2025-06-03 11:23:55] default.INFO: 托福成绩:  [] []
[2025-06-03 11:23:55] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:23:55] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:29:57] default.INFO: toggleAIOverlay 请求参数: {"name":"测试学生3","sex":"1","phone":"13966311243","undergraduateSchool":"71","undergraduateSchoolName":"合肥工业大学","undergraduateMajor":"3650","undergraduateMajorName":"计算机科学与技术","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":85}],"undergraduateTranscript":[{"id":1,"title":"英语","score":80},{"id":2,"title":"数学","score":85}],"englishScore":"120","cet4":"140","cet6":"130","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"浙江省,江苏省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"80","mathType":"数学一","mathScore":"110","professionalScore":"110","totalScore":"370","personalNeeds":"没有","weakModules":""} [] []
[2025-06-03 11:29:57] default.INFO: 四级成绩: 140 [] []
[2025-06-03 11:29:57] default.INFO: 六级成绩: 130 [] []
[2025-06-03 11:29:57] default.INFO: 托福成绩:  [] []
[2025-06-03 11:29:57] default.INFO: 英语能力: 一般 [] []
[2025-06-03 11:29:57] default.INFO: 地区倾向: A区 [] []
[2025-06-03 11:42:21] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 11:42:21] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 11:42:21"} []
[2025-06-03 11:43:57] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 11:43:57] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 11:43:57"} []
[2025-06-03 11:51:09] default.INFO: 获取学生列表请求参数: {"name":"\u67e5","page":"1","limit":"10"} [] []
[2025-06-03 12:04:10] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 12:43:57] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 13:32:37] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 13:40:14] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 13:43:57] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 14:05:22] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-06-03 14:05:25] default.INFO: 获取学生列表请求参数: {"name":"\u67e5","page":"1","limit":"10"} [] []
[2025-06-03 14:05:46] default.INFO: toggleAIOverlay 请求参数: {"name":"查志宇","sex":"1","phone":"13083409461","undergraduateSchool":167,"undergraduateSchoolName":"安徽大学","undergraduateMajor":9533,"undergraduateMajorName":"国际经济与贸易","targetMajor":"(025400)国际商务","targetMajorName":"(025400)国际商务","majorCode":"025400","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":"62"}],"undergraduateTranscript":[{"id":1,"title":"英语","score":"62"}],"englishScore":"129","cet4":"520","cet6":"410","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"91","targetSchoolName":"上海财经大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"60","mathType":"数学一","mathScore":"100","professionalScore":"120","totalScore":"350","personalNeeds":"没有","weakModules":"","student_id":4} [] []
[2025-06-03 14:05:46] default.INFO: 四级成绩: 520 [] []
[2025-06-03 14:05:46] default.INFO: 六级成绩: 410 [] []
[2025-06-03 14:05:46] default.INFO: 托福成绩:  [] []
[2025-06-03 14:05:46] default.INFO: 英语能力: 一般 [] []
[2025-06-03 14:05:46] default.INFO: 地区倾向: A区 [] []
[2025-06-03 14:06:40] default.INFO: 获取学生列表请求参数: {"name":"\u67e5\u5fd7\u5b87","page":"1","limit":"10"} [] []
[2025-06-03 14:07:10] default.INFO: toggleAIOverlay 请求参数: {"name":"查志宇","sex":"1","phone":"13083409461","undergraduateSchool":167,"undergraduateSchoolName":"安徽大学","undergraduateMajor":9533,"undergraduateMajorName":"国际经济与贸易","targetMajor":"(025400)国际商务","targetMajorName":"(025400)国际商务","majorCode":"025400","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":"62"}],"undergraduateTranscript":[{"id":1,"title":"英语","score":"62"}],"englishScore":"129","cet4":"520","cet6":"410","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"91","targetSchoolName":"上海财经大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"60","mathType":"数学一","mathScore":"100","professionalScore":"120","totalScore":"350","personalNeeds":"没有","weakModules":"","student_id":4} [] []
[2025-06-03 14:07:11] default.INFO: 四级成绩: 520 [] []
[2025-06-03 14:07:11] default.INFO: 六级成绩: 410 [] []
[2025-06-03 14:07:11] default.INFO: 托福成绩:  [] []
[2025-06-03 14:07:11] default.INFO: 英语能力: 一般 [] []
[2025-06-03 14:07:11] default.INFO: 地区倾向: A区 [] []
[2025-06-03 14:07:13] default.INFO: 获取国家线数据请求参数: {"major_code":"025400"} [] []
[2025-06-03 14:07:13] default.INFO: 流式AI推荐请求参数: {"report_id":"365"} [] []
[2025-06-03 14:07:27] default.INFO: 获取学生列表请求参数: {"name":"\u67e5","page":"1","limit":"10"} [] []
[2025-06-03 14:07:38] default.INFO: toggleAIOverlay 请求参数: {"name":"查志宇","sex":"1","phone":"13083409461","undergraduateSchool":167,"undergraduateSchoolName":"安徽大学","undergraduateMajor":9533,"undergraduateMajorName":"国际经济与贸易","targetMajor":"(025400)国际商务","targetMajorName":"(025400)国际商务","majorCode":"025400","examYear":"2027","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":"62"}],"undergraduateTranscript":[{"id":1,"title":"英语","score":"62"}],"englishScore":"129","cet4":"520","cet6":"410","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"江苏省,上海市","targetSchool":"91","targetSchoolName":"上海财经大学","schoolLevel":"211","referenceBooks":"无","politics":"70","englishType":"英语一","englishS":"60","mathType":"数学一","mathScore":"100","professionalScore":"120","totalScore":"350","personalNeeds":"没有","weakModules":"","student_id":4} [] []
[2025-06-03 14:07:38] default.INFO: 四级成绩: 520 [] []
[2025-06-03 14:07:38] default.INFO: 六级成绩: 410 [] []
[2025-06-03 14:07:38] default.INFO: 托福成绩:  [] []
[2025-06-03 14:07:38] default.INFO: 英语能力: 一般 [] []
[2025-06-03 14:07:38] default.INFO: 地区倾向: A区 [] []
[2025-06-03 14:07:38] default.INFO: 获取国家线数据请求参数: {"major_code":"025400"} [] []
[2025-06-03 14:07:38] default.INFO: 流式AI推荐请求参数: {"report_id":"366"} [] []
[2025-06-03 14:08:04] default.INFO: 找到学校数据: 上海财经大学, ID: 91, logo:  [] []
[2025-06-03 14:09:04] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 14:09:04] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 14:09:04"} []
[2025-06-03 15:09:04] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":false,\"message\":\"登录将在后台执行，请稍后再次检查登录状态\",\"login_time\":null}"} []
[2025-06-03 16:09:05] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 17:09:05] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 17:11:37] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 17:15:35] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-06-03 17:15:55] default.INFO: toggleAIOverlay 请求参数: {"name":"张珊","sex":"2","phone":"19855196869","undergraduateSchool":196,"undergraduateSchoolName":"安徽师范大学","undergraduateMajor":11051,"undergraduateMajorName":"计算机科学与技术","targetMajor":"(081200)计算机科学与技术","targetMajorName":"(081200)计算机科学与技术","majorCode":"081200","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"undergraduateTranscript":[{"id":1,"title":"数学","score":80},{"id":2,"title":"英语","score":82}],"englishScore":"144","cet4":"481","cet6":"510","tofelScore":"","ieltsScore":"","englishAbility":"good","targetRegion":"A区","region":"A区","targetProvinces":"天津市,江苏省,安徽省","targetSchool":"14","targetSchoolName":"中国科学技术大学","schoolLevel":"211","referenceBooks":"无","politics":"65","englishType":"英语一","englishS":"65","mathType":"数学一","mathScore":"110","professionalScore":"100","totalScore":"340","personalNeeds":"没有","weakModules":"","student_id":2} [] []
[2025-06-03 17:15:55] default.INFO: 四级成绩: 481 [] []
[2025-06-03 17:15:55] default.INFO: 六级成绩: 510 [] []
[2025-06-03 17:15:55] default.INFO: 托福成绩:  [] []
[2025-06-03 17:15:55] default.INFO: 英语能力: good [] []
[2025-06-03 17:15:55] default.INFO: 地区倾向: A区 [] []
[2025-06-03 17:15:56] default.INFO: 获取国家线数据请求参数: {"major_code":"081200"} [] []
[2025-06-03 17:15:57] default.INFO: 流式AI推荐请求参数: {"report_id":"370"} [] []
[2025-06-03 17:16:41] default.INFO: 找到学校数据: 南京理工大学, ID: 63, logo:  [] []
[2025-06-03 17:16:59] default.INFO: 找到学校数据: 东华大学, ID: 83, logo:  [] []
[2025-06-03 17:17:24] default.INFO: 找到学校数据: 河海大学, ID: 57, logo:  [] []
[2025-06-03 17:17:26] default.INFO: 报告数据保存成功，报告ID: 370，共插入 5 条记录 [] []
[2025-06-03 17:20:03] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 17:55:48] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:09:06] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 18:14:29] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:14:39] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:16:19] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:29:45] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:41:23] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:42:48] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:43:16] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:43:18] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
[2025-06-03 18:54:46] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":true,\"message\":\"当前会话有效，无需重新登录\",\"login_time\":null}"} []
[2025-06-03 18:54:46] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-06-03 18:54:46"} []
[2025-06-03 18:55:15] default.INFO: 获取学生列表请求参数: {"page":"1","limit":"10","name":"","phone":"","teacherId":"","school":"","undergraduate":"","undergraduateMajor":"","targetSchool":"","targetMajor":""} [] []
