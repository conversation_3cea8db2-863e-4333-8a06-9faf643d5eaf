<?php
use Webman\Route;
// 不需要鉴权的接口
Route::get('/api/school/score', [app\controller\SchoolController::class, 'getSchoolsByScore']);
Route::get('/api/school/national-line', [app\controller\SchoolController::class, 'getNationalLineData']);
// SSE测试接口，不需要鉴权
Route::get('/api/remote/stream_ai_recommendation', [app\controller\RemoteController::class, 'streamAiRecommendation'])
->middleware(middleware: [app\middleware\CorsMiddleware::class]);


Route::post('/api/remote/get_school_detail', [app\controller\RemoteController::class, 'getSchoolDetail']);
Route::group("/api", function () {
    Route::post('/login', [app\controller\UserController::class, 'login']);
    Route::post('/logout', [app\controller\UserController::class, 'logout']);
    Route::get('/school/search', [app\controller\SchoolController::class, 'search']);
    Route::get('/school/major/search', [app\controller\SchoolController::class, 'searchSchoolMajor']);
    Route::get('/major/search', [app\controller\SchoolController::class, 'searchMajor']);
    Route::get('/exam/years', [app\controller\SchoolController::class, 'getExamYears']);

    // 标签管理相关接口
    Route::get('/tag/all', [app\controller\TagController::class, 'getAll']);
    Route::post('/tag/add', [app\controller\TagController::class, 'add']);
    Route::post('/tag/edit', [app\controller\TagController::class, 'edit']);
    Route::get('/tag/delete', [app\controller\TagController::class, 'delete']);

    // 学生管理相关接口
    Route::get('/student/list', [app\controller\StudentController::class, 'getList']);
    Route::get('/student/detail', [app\controller\StudentController::class, 'getDetail']);
    Route::post('/student/add', [app\controller\StudentController::class, 'add']);
    Route::post('/student/edit', [app\controller\StudentController::class, 'edit']);
    Route::get('/student/delete', [app\controller\StudentController::class, 'delete']);
    Route::post('/student/update-teacher', [app\controller\StudentController::class, 'updateTeacher']);
    Route::get('/student/test', [app\controller\StudentController::class, 'test']);
    //创建报告记录
    Route::post('/student/toggle-ai-overlay', [app\controller\StudentController::class, 'toggleAIOverlay']);

    // 用户管理相关接口
    Route::get('/user/teacher-list', [app\controller\UserController::class, 'getTeacherList']);
    Route::get('/user/search-teacher', [app\controller\UserController::class, 'searchTeacher']);
    Route::get('/user/info', [app\controller\UserController::class, 'getUserInfo']);
    Route::get('/user/verify-token', [app\controller\UserController::class, 'verifyToken']);

    // 用户管理相关接口
    Route::post('/remote/get_msg_str', [app\controller\RemoteController::class, 'getMsgStr']);

    Route::post('/remote/school_and_major', [app\controller\RemoteController::class, 'schoolAndMajor']);

    // AI推荐学校接口
    Route::post('/remote/ai_recommendation', [app\controller\RemoteController::class, 'getAiRecommendation']);

})->middleware([app\middleware\JwtMiddleware::class]);
