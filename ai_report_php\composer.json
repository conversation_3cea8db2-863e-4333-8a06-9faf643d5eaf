{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.1", "workerman/webman-framework": "^2.1", "monolog/monolog": "^2.0", "webman/think-orm": "^2.1", "webman/redis": "^2.1", "illuminate/events": "^11.44", "webman/cache": "^2.1", "vlucas/phpdotenv": "^5.6", "firebase/php-jwt": "^6.11", "webman/console": "1.2.13", "webman/think-cache": "^2.1", "qcloud_sts/qcloud-sts-sdk": "3.0.*"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true}