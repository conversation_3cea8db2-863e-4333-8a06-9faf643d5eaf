<template>
  <div class="generate-container">
    <div class="report-container">
      <div class="steps">
        <!-- 第一步：个人基础信息 -->
        <div class="step-section form-section" id="section-0">
          <div class="step-header">
            <div class="step-title">第一部分：个人基础信息</div>
          </div>

          <div class="step-content">
            <div class="step-num-tag">
              <span>01</span>
              <div class="tag-text">个人基础信息</div>
            </div>

            <div class="form-grid">
              <div class="form-item">
                <div class="item-label">学员姓名</div>
                <el-input
                  v-model="props.studentForm.name"
                  placeholder="请输入学员姓名"
                  :disabled="props.dialogType === 'detail'"
                ></el-input>
              </div>

              <div class="form-item">
                <div class="item-label">性别</div>
                <template v-if="props.dialogType === 'detail'">
                  <el-input
                    :value="getSexText(props.studentForm.sex)"
                    disabled
                    class="full-width"
                  ></el-input>
                </template>
                <template v-else>
                  <el-select
                    v-model="displaySex"
                    placeholder="请选择性别"
                    class="full-width"
                  >
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="2"></el-option>
                    <el-option label="其他" value="3"></el-option>
                  </el-select>
                </template>
              </div>

              <div class="form-item">
                <div class="item-label">本科院校</div>
                <el-select
                  v-model="props.studentForm.undergraduateSchool"
                  placeholder="输入关键字搜索院校"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteSearchCollege"
                  :loading="props.schoolSearchLoading"
                  class="full-width"
                  @change="handleCollegeChange"
                >
                  <el-option
                    v-for="item in props.schoolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">本科专业</div>
                <el-select
                  v-model="props.studentForm.undergraduateMajor"
                  placeholder="输入关键字搜索专业"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteSearchMajor"
                  :loading="props.majorSearchLoading"
                  class="full-width"
                  :disabled="!props.studentForm.undergraduateSchool"
                  @change="handleMajorChange"
                >
                  <el-option
                    v-for="item in props.majorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <!-- 始终显示本科专业名称，无论是什么模式 -->
              </div>

              <div class="form-item">
                <div class="item-label">目标专业</div>
                <el-select
                  v-model="props.studentForm.targetMajor"
                  placeholder="输入关键字搜索专业"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteSearchTargetMajor"
                  :loading="props.targetMajorSearchLoading"
                  class="full-width"
                  @change="handleTargetMajorChange"
                >
                  <el-option
                    v-for="item in props.targetMajorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">专业代码</div>
                <el-input
                  v-model="props.studentForm.majorCode"
                  placeholder="请输入专业代码"
                ></el-input>
              </div>

              <div class="form-item">
                <div class="item-label">联系方式</div>
                <el-input
                  v-model="props.studentForm.phone"
                  placeholder="请输入联系方式"
                ></el-input>
              </div>

              <div class="form-item">
                <div class="item-label">考研年份</div>
                <el-select
                  v-model="props.studentForm.examYear"
                  placeholder="请选择考研年份"
                  class="full-width"
                  :loading="examYearLoading"
                >
                  <el-option
                    v-for="item in examYearOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">跨专业</div>
                <el-select
                  v-model="props.studentForm.isMultiDisciplinary"
                  placeholder="请选择是否跨专业"
                  class="full-width"
                >
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="2"></el-option>
                </el-select>
              </div>

              <div class="form-item wide-item">
                <div class="item-label">标签</div>
                <el-cascader
                  v-model="props.studentForm.tags"
                  :options="tagCascaderOptions"
                  placeholder="请选择标签"
                  clearable
                  filterable
                  :props="{
                    checkStrictly: true,
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    expandTrigger: 'hover',
                    multiple: true,
                    emitPath: false,
                  }"
                  :disabled="props.dialogType === 'detail'"
                  class="full-width"
                  @change="handleTagsChange"
                ></el-cascader>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二步：本科成绩情况 -->
        <div class="step-section form-section" id="section-1">
          <div class="step-content">
            <div class="step-container">
              <div class="step-num-tag">
                <span>02</span>
                <div class="tag-text">本科成绩情况</div>
              </div>
              <div class="create-btn">
                <el-button
                  type="primary"
                  class="action-button"
                  @click="openAddScoreDialog"
                  >创建</el-button
                >
              </div>
            </div>

            <div class="score-grid">
              <div class="score-row">
                <div
                  class="score-item"
                  v-for="item in props.studentForm.undergraduateTranscript"
                  :key="item.id"
                  :draggable="true"
                  @dragstart="handleDragStart($event, item)"
                  @dragover.prevent="handleDragOver($event)"
                  @drop="handleDrop($event, item)"
                  @dragend="handleDragEnd"
                >
                  <div class="score-label">{{ item.title }}</div>
                  <div class="score-input-container">
                    <el-input
                      v-model="item.score"
                      placeholder="请输入成绩"
                    ></el-input>
                  </div>
                  <el-icon
                    @click="delUndergraduateTranscript(item.id)"
                    class="score-close"
                    color="#1BB394"
                    size="14"
                  >
                    <Close />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三步：英语基础 -->
        <div class="step-section form-section" id="section-2">
          <div class="step-content">
            <div class="step-num-tag">
              <span>03</span>
              <div class="tag-text">英语基础</div>
            </div>

            <div class="english-grid">
              <div class="english-row">
                <div class="english-item">
                  <div class="english-label">高考英语成绩</div>
                  <el-input
                    v-model="props.studentForm.englishScore"
                    placeholder="请输入高考英语成绩"
                    class="full-width"
                  ></el-input>
                </div>

                <div class="english-item">
                  <div class="english-label">大学四级</div>
                  <el-input
                    v-model="props.studentForm.cet4"
                    placeholder="请输入四级成绩"
                    class="full-width"
                  ></el-input>
                </div>

                <div class="english-item">
                  <div class="english-label">大学六级</div>
                  <el-input
                    v-model="props.studentForm.cet6"
                    placeholder="请输入六级成绩"
                    class="full-width"
                  ></el-input>
                </div>

                <div class="english-item">
                  <div class="english-label">托福</div>
                  <el-input
                    v-model="props.studentForm.tofelScore"
                    placeholder="请输入托福成绩"
                    class="full-width"
                  ></el-input>
                </div>
              </div>

              <div class="english-row">
                <div class="english-item">
                  <div class="english-label">雅思</div>
                  <el-input
                    v-model="props.studentForm.ieltsScore"
                    placeholder="请输入雅思成绩"
                    class="full-width"
                  ></el-input>
                </div>

                <div class="english-item">
                  <div class="english-label">英语能力</div>
                  <el-select
                    v-model="props.studentForm.englishAbility"
                    placeholder="请选择英语能力"
                    class="full-width"
                    popper-class="english-ability-dropdown"
                  >
                    <el-option label="一般" value="一般"></el-option>
                    <el-option label="良好" value="良好"></el-option>
                    <el-option label="优秀" value="优秀"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第四步：目标院校梯度 -->
        <div class="step-section form-section" id="section-3">
          <div class="step-content">
            <div class="step-num-tag">
              <span>04</span>
              <div class="tag-text">目标院校梯度</div>
            </div>

            <div class="school-grid">
              <div class="form-item">
                <div class="item-label">地区倾向</div>
                <el-select
                  v-model="props.studentForm.region"
                  placeholder="请选择地区倾向"
                  multiple
                  class="full-width"
                >
                  <el-option label="A区" value="A区"></el-option>
                  <el-option label="B区" value="B区"></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">省份选择</div>
                <el-select
                  v-model="props.studentForm.targetProvinces"
                  placeholder="请选择省份"
                  multiple
                  class="full-width"
                >
                  <el-option
                    v-for="item in provinceData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">梦校</div>
                <el-select
                  v-model="props.studentForm.targetSchool"
                  placeholder="输入关键字搜索院校"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteSearchDreamSchool"
                  :loading="dreamSchoolSearchLoading"
                  class="full-width"
                  @change="handleDreamSchoolChange"
                >
                  <el-option
                    v-for="item in dreamSchoolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">院校层次</div>
                <el-select
                  v-model="props.studentForm.schoolLevel"
                  placeholder="请选择院校层次"
                  class="full-width"
                >
                  <el-option label="985" value="985"></el-option>
                  <el-option label="211" value="211"></el-option>
                  <el-option label="双一流" value="双一流"></el-option>
                </el-select>
              </div>

              <div class="form-item wide-item">
                <div class="item-label">专业课指定参考书</div>
                <el-input
                  v-model="props.studentForm.referenceBooks"
                  placeholder="请输入专业课指定参考书"
                ></el-input>
              </div>
            </div>
          </div>
        </div>

        <!-- 第五步：考研成绩预估 -->
        <div class="step-section form-section" id="section-4">
          <div class="step-content">
            <div class="step-num-tag">
              <span>05</span>
              <div class="tag-text">考研成绩预估</div>
            </div>

            <div class="score-table">
              <div class="table-header">
                <div class="th-cell">政治</div>
                <div class="th-cell">
                  <el-select
                    class="sel-no-border"
                    v-model="englishMajor"
                    placeholder="请选择英语"
                    size="large"
                  >
                    <el-option
                      v-for="item in EnglishMajorOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="th-cell">
                  <el-select
                    v-model="mathMajor"
                    placeholder="请选择专业课"
                    size="large"
                    class="sel-no-border"
                  >
                    <el-option
                      v-for="item in MathMajorOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="th-cell">专业课</div>
                <div class="th-cell">总分</div>
              </div>
              <div class="table-row-score">
                <div class="td-cell">
                  <el-input
                    v-model="props.studentForm.politics"
                    class="table-input"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="props.studentForm.englishS"
                    class="table-input"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="props.studentForm.mathScore"
                    class="table-input"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="props.studentForm.professionalScore"
                    class="table-input"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="props.studentForm.totalScore"
                    class="table-input"
                    disabled
                  ></el-input>
                </div>
              </div>
            </div>

            <div class="personal-demands">
              <div class="demands-label">个性化需求</div>
              <el-input
                v-model="props.studentForm.personalNeeds"
                type="textarea"
                :rows="1"
                placeholder="请输入个性化需求"
                class="demands-input"
              ></el-input>
            </div>

            <div class="expertise-advice form-section" id="section-5">
              <div class="advice-label">薄弱模块</div>
              <el-input
                v-model="props.studentForm.weakModules"
                type="textarea"
                :rows="1"
                placeholder="请输入薄弱模块"
                class="advice-input"
              ></el-input>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加成绩对话框 -->
    <el-dialog
      title="添加成绩项"
      v-model="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="newScoreForm" label-width="80px">
        <el-form-item label="科目名称">
          <el-input
            v-model="newScoreForm.title"
            placeholder="请输入科目名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="成绩">
          <el-input
            v-model="newScoreForm.score"
            placeholder="请输入成绩"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            style="background-color: #1bb394"
            @click="addNewScore"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  nextTick,
  watch,
  defineProps,
  defineEmits,
  computed,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  searchSchool,
  searchMajor,
  searchTargetMajor,
  getExamYears,
} from "@/api/school";
import { getAllTags } from "@/api/tag";
import * as echarts from "echarts";
import provinceData from "@/utils/province.json";

// 定义props
const props = defineProps({
  studentForm: {
    type: Object,
    required: true,
  },
  dialogType: {
    type: String,
    default: "add",
  },
  schoolOptions: {
    type: Array,
    default: () => [],
  },
  majorOptions: {
    type: Array,
    default: () => [],
  },
  targetMajorOptions: {
    type: Array,
    default: () => [],
  },
  tagOptions: {
    type: Array,
    default: () => [],
  },
  schoolSearchLoading: {
    type: Boolean,
    default: false,
  },
  majorSearchLoading: {
    type: Boolean,
    default: false,
  },
  targetMajorSearchLoading: {
    type: Boolean,
    default: false,
  },
});


// 定义事件
const emit = defineEmits([
  "school-change",
  "search-school",
  "search-major",
  "search-target-major",
]);

// 加载状态
const loading = ref(false);
const collegeSearchLoading = ref(false);
const collegeOptions = ref([]);
const collegeSearchTimeout = ref(null);
const collegeSearchCache = {};

// 考研年份列表
const examYearOptions = ref([]);
const examYearLoading = ref(false);

// 梦校搜索相关
const dreamSchoolOptions = ref([]);
const dreamSchoolSearchLoading = ref(false);
const dreamSchoolSearchTimeout = ref(null);
const dreamSchoolSearchCache = {};

// 标签级联选择器数据
const tagCascaderOptions = ref([]);
const allTags = ref([]);

const majorSearchLoading = ref(false);
const majorOptions = ref([]);
const majorSearchTimeout = ref(null);
const majorSearchCache = {};

const targetMajorSearchLoading = ref(false);
const targetMajorOptions = ref([]);
const targetMajorSearchTimeout = ref(null);
const targetMajorSearchCache = {};

// 表单数据
const reportForm = reactive({
  // 第一步：个人基础信息
  studentName: "",
  gender: "",
  college: "",
  major: "",
  targetMajor: "",
  majorCode: "",
  contact: "",
  year: "",
  crossMajor: "",

  // 第二步：本科成绩情况
  gaokaoScore: "",
  mathScore1: "",
  mathScore2: "",
  statScore: "",
  linearScore: "",
  majorScore1: "",
  majorScore2: "",
  majorScore3: "",

  // 第三步：英语基础
  gaokaoEnglish: "",
  cet4: "",
  cet6: "",
  toefl: "",
  ielts: "",
  englishLevel: "",
  englishAbility: "",

  // 第四步：目标院校梯度
  region: "",
  intendedSchools: [],
  targetSchool: "",
  schoolLevel: "",
  majorAdjustment: "",

  // 第五步：考研成绩预估
  politics: "",
  english: "",
  statistician: "",
  professional: "",
  totalScore: "",
  personalDemands: "",
  expertiseAdvice: "",
});

const EnglishMajorOptions = [
  { value: "0", label: "英语一" },
  { value: "1", label: "英语二" },
];
let englishMajor = ref("");
let mathMajor = ref("");
const MathMajorOptions = [
  { value: "0", label: "数学一" },
  { value: "1", label: "数学二" },
  { value: "2", label: "数学三" },
  { value: "3", label: "199管理类联考" },
  { value: "4", label: "396经济类联考" },
];

// 加载所有标签
const loadAllTags = () => {
  getAllTags()
    .then((res) => {
      if (res.code === 0) {
        // 获取各层级标签
        const firstLevel = res.data.first || [];
        const secondLevel = res.data.second || [];
        const thirdLevel = res.data.third || [];

        // 将所有标签合并到一个数组
        allTags.value = [...firstLevel, ...secondLevel, ...thirdLevel];

        // 构建级联选择器数据结构
        tagCascaderOptions.value = firstLevel.map((firstTag) => {
          // 找出属于当前一级标签的所有二级标签
          const children = secondLevel
            .filter((secondTag) => secondTag.parent_id === firstTag.id)
            .map((secondTag) => {
              // 找出属于当前二级标签的所有三级标签
              const grandChildren = thirdLevel
                .filter((thirdTag) => thirdTag.parent_id === secondTag.id)
                .map((thirdTag) => ({
                  id: thirdTag.id,
                  name: thirdTag.name,
                  color: thirdTag.color || firstTag.color,
                }));

              return {
                id: secondTag.id,
                name: secondTag.name,
                color: secondTag.color || firstTag.color,
                children: grandChildren.length > 0 ? grandChildren : undefined,
              };
            });

          return {
            id: firstTag.id,
            name: firstTag.name,
            color: firstTag.color,
            children: children.length > 0 ? children : undefined,
          };
        });
      } else {
        ElMessage.error(res.msg || "获取标签失败");
      }
    })
    .catch((err) => {
      console.error("获取标签失败", err);
      ElMessage.error("获取标签失败，请稍后重试");
    });
};

// 获取标签样式类
const getTagClass = (tag) => {
  if (tag.class) return tag.class;

  // 根据颜色值返回对应的样式类
  switch (tag.color) {
    case "green":
      return "tag-green";
    case "orange":
      return "tag-orange";
    case "purple":
      return "tag-purple";
    case "blue":
      return "tag-blue";
    default:
      return "tag-default";
  }
};

// 计算标签显示
const displayTags = computed(() => {
  if (
    !props.studentForm.tags ||
    !Array.isArray(props.studentForm.tags) ||
    props.studentForm.tags.length === 0
  ) {
    return [];
  }

  // 处理级联选择器选中的标签
  return props.studentForm.tags.map((tagId) => {
    // 先在所有标签中查找
    const foundTag = allTags.value.find((tag) => tag.id === tagId);
    if (foundTag) {
      return {
        value: foundTag.id,
        label: foundTag.name,
        color: foundTag.color,
      };
    }

    // 如果在所有标签中找不到，可能是旧数据，尝试在props.tagOptions中查找
    const tagOption = props.tagOptions.find((option) => option.value === tagId);
    return tagOption || { value: tagId, label: tagId, class: "tag-default" };
  });
});

// 性别显示计算属性
const displaySex = computed({
  get: () => {
    return props.studentForm.sex;
  },
  set: (value) => {
    // 确保值是字符串类型
    props.studentForm.sex =
      value !== null && value !== undefined ? String(value) : value;
  },
});

// 同步reportForm和studentForm
watch(
  () => props.studentForm,
  (newVal) => {
    console.log("newval", newVal);
    // 确保所有字段都有值，避免显示null
    // 将studentForm的值同步到reportForm
    reportForm.studentName = newVal.name === null ? "" : newVal.name || "";
    // 性别字段已直接使用 props.studentForm.sex
    reportForm.college =
      newVal.undergraduateSchool === null
        ? ""
        : newVal.undergraduateSchool || "";
    reportForm.major =
      newVal.undergraduateMajor === null ? "" : newVal.undergraduateMajor || "";
    reportForm.targetMajor =
      newVal.targetMajor === null ? "" : newVal.targetMajor || "";
    reportForm.majorCode =
      newVal.majorCode === null ? "" : newVal.majorCode || "";
    reportForm.contact = newVal.phone === null ? "" : newVal.phone || "";
    reportForm.year = newVal.examYear === null ? "" : newVal.examYear || "";
    reportForm.crossMajor =
      newVal.isMultiDisciplinary === null
        ? ""
        : newVal.isMultiDisciplinary || "";

    // 确保studentForm中的字段不为null
    if (newVal.name === null) props.studentForm.name = "";

    // 处理性别字段，确保是字符串类型
    if (newVal.sex === null) {
      props.studentForm.sex = "";
    } else if (typeof newVal.sex !== "string") {
      props.studentForm.sex = String(newVal.sex);
    }

    if (newVal.undergraduateSchool === null)
      props.studentForm.undergraduateSchool = "";
    if (newVal.undergraduateMajor === null)
      props.studentForm.undergraduateMajor = "";
    if (newVal.targetMajor === null) props.studentForm.targetMajor = "";
    if (newVal.majorCode === null) props.studentForm.majorCode = "";
    if (newVal.phone === null) props.studentForm.phone = "";
    if (newVal.examYear === null) props.studentForm.examYear = "";
    if (newVal.isMultiDisciplinary === null)
      props.studentForm.isMultiDisciplinary = "";

    // 确保英语能力有值
    if (newVal.englishAbility === null || newVal.englishAbility === undefined) {
      props.studentForm.englishAbility = "";
    }

    // 确保专业课指定参考书有值
    if (newVal.referenceBooks === null || newVal.referenceBooks === undefined) {
      props.studentForm.referenceBooks = "";
    }

    // 确保省份选择有值
    if (
      newVal.targetProvinces === null ||
      newVal.targetProvinces === undefined
    ) {
      props.studentForm.targetProvinces = [];
    } else if (typeof newVal.targetProvinces === "string") {
      // 如果是字符串，尝试解析
      props.studentForm.targetProvinces = newVal.targetProvinces
        .split(",")
        .filter((p) => p.trim() !== "");
    } else if (Array.isArray(newVal.targetProvinces)) {
      props.studentForm.targetProvinces = newVal.targetProvinces;
    }

    // 确保地区倾向有值，但避免递归更新
    const newRegion = newVal.targetRegion || [];
    // 只在控制台输出日志，不修改 props.studentForm.region
    console.log("当前地区倾向:", newRegion);
    // 如果是编辑模式，确保在schoolOptions中添加当前选中的学校
    if (
      props.dialogType === "edit" &&
      props.studentForm.undergraduateSchool &&
      props.studentForm.undergraduateSchoolName
    ) {
      // 检查是否已经存在该选项
      const existingSchool = props.schoolOptions.find(
        (item) => item.value === props.studentForm.undergraduateSchool
      );
      if (!existingSchool) {
        // 添加当前学校到选项中
        props.schoolOptions.push({
          value: props.studentForm.undergraduateSchool,
          label: props.studentForm.undergraduateSchoolName,
        });
      }
    }

    // 如果是编辑模式，确保在majorOptions中添加当前选中的专业
    if (
      props.dialogType === "edit" &&
      props.studentForm.undergraduateMajor &&
      props.studentForm.undergraduateMajorName
    ) {
      // 检查是否已经存在该选项
      const existingMajor = props.majorOptions.find(
        (item) => item.value === props.studentForm.undergraduateMajor
      );
      if (!existingMajor) {
        // 添加当前专业到选项中
        props.majorOptions.push({
          value: props.studentForm.undergraduateMajor,
          label: props.studentForm.undergraduateMajorName,
        });
      }
    }

    // 如果是编辑模式，确保在targetMajorOptions中添加当前选中的目标专业
    if (
      props.dialogType === "edit" &&
      props.studentForm.targetMajor &&
      props.studentForm.targetMajorName
    ) {
      // 检查是否已经存在该选项
      const existingTargetMajor = props.targetMajorOptions.find(
        (item) => item.value === props.studentForm.targetMajor
      );
      if (!existingTargetMajor) {
        // 添加当前目标专业到选项中
        props.targetMajorOptions.push({
          value: props.studentForm.targetMajor,
          label: props.studentForm.targetMajorName,
          code: props.studentForm.majorCode || "",
        });
      }
    }

    // 如果是编辑模式，确保在dreamSchoolOptions中添加当前选中的梦校
    if (
      props.dialogType === "edit" &&
      props.studentForm.targetSchool &&
      props.studentForm.targetSchoolName
    ) {
      // 检查是否已经存在该选项
      const existingDreamSchool = dreamSchoolOptions.value.find(
        (item) => item.value === props.studentForm.targetSchool
      );
      if (!existingDreamSchool) {
        // 添加当前梦校到选项中
        dreamSchoolOptions.value.push({
          value: props.studentForm.targetSchool,
          label: props.studentForm.targetSchoolName,
        });
      }
    }

    if (
      props.dialogType == "edit" &&
      props.studentForm.mathType &&
      props.studentForm.englishType
    ) {
      const typeMathValue = MathMajorOptions.find(
        (item) => item.label == props.studentForm.mathType
      );
      // // 设置数学类型下拉框
      mathMajor.value = typeMathValue ? typeMathValue.value : "0";

      const typeEnglishValue = EnglishMajorOptions.find(
        (item) => item.label == props.studentForm.englishType
      );

      console.log(typeEnglishValue);
      // // 设置英语类型下拉框
      englishMajor.value = typeEnglishValue ? typeEnglishValue.value : "0";
    }
  },
  { deep: true, immediate: true }
);

// 同步reportForm的studentName到studentForm的name
watch(
  () => reportForm.studentName,
  (newVal) => {
    props.studentForm.name = newVal;
  }
);

// 监听考研成绩变化，自动计算总分
watch(
  [
    () => props.studentForm.politics,
    () => props.studentForm.englishS,
    () => props.studentForm.mathScore,
    () => props.studentForm.professionalScore,
  ],
  ([politics, englishS, mathS, professionalS]) => {
    // 将空字符串转换为0
    const politicsScore = politics ? parseFloat(politics) : 0;
    const englishScore = englishS ? parseFloat(englishS) : 0;
    const mathScore = mathS ? parseFloat(mathS) : 0;
    const professionalScore = professionalS ? parseFloat(professionalS) : 0;
    props.studentForm.totalScore =
      politicsScore + englishScore + mathScore + professionalScore;
  }
);

let scoreInfo = ref([]);

// 添加成绩相关
const dialogVisible = ref(false);
const newScoreForm = reactive({
  title: "",
  score: "",
});

// 拖拽相关状态
const draggedItem = ref(null);

// 拖拽开始
const handleDragStart = (event, item) => {
  draggedItem.value = item;
  event.dataTransfer.effectAllowed = "move";
  // 设置拖拽时的半透明效果
  event.target.style.opacity = "0.5";
};

// 拖拽经过
const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = "move";

  // 添加拖拽经过的视觉反馈
  const targetElement = event.currentTarget;
  if (
    targetElement &&
    draggedItem.value &&
    targetElement !== draggedItem.value
  ) {
    targetElement.classList.add("drag-over");
  }
};

// 拖拽放置
const handleDrop = (event, targetItem) => {
  event.preventDefault();

  // 移除所有元素的拖拽效果类
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });

  if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
    return;
  }

  // 找到拖拽项和目标项的索引
  const dragIndex = props.studentForm.undergraduateTranscript.findIndex(
    (item) => item.id === draggedItem.value.id
  );
  const targetIndex = props.studentForm.undergraduateTranscript.findIndex(
    (item) => item.id === targetItem.id
  );

  if (dragIndex < 0 || targetIndex < 0) {
    return;
  }

  // 重新排序
  const newScoreInfo = [...props.studentForm.undergraduateTranscript];
  const [removed] = newScoreInfo.splice(dragIndex, 1);
  newScoreInfo.splice(targetIndex, 0, removed);

  // 更新成绩列表
  props.studentForm.undergraduateTranscript = newScoreInfo;

  // 清除拖拽状态
  draggedItem.value = null;

  // 恢复透明度
  document.querySelectorAll(".score-item").forEach((el) => {
    el.style.opacity = "1";
  });

  ElMessage.success("排序已更新");
};

// 拖拽结束
const handleDragEnd = (event) => {
  // 恢复透明度
  event.target.style.opacity = "1";

  // 可以添加拖拽结束的视觉反馈
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });
};

// 打开添加成绩对话框
const openAddScoreDialog = () => {
  dialogVisible.value = true;
  // 重置表单
  newScoreForm.title = "";
  newScoreForm.score = "";
};

// 删除本科成绩
const delUndergraduateTranscript = (id) => {
  if (props.studentForm.undergraduateTranscript.length <= 1) {
    ElMessage.warning("至少保留一项成绩");
    return;
  }

  const index = props.studentForm.undergraduateTranscript.findIndex(
    (item) => item.id === id
  );
  if (index !== -1) {
    props.studentForm.undergraduateTranscript.splice(index, 1);
    ElMessage.success("删除成功");
  }
};

// 删除数学成绩 (保留兼容性)
const delMathScore = (id) => {
  delUndergraduateTranscript(id);
};

// 添加新成绩
const addNewScore = () => {
  // 验证表单
  if (!newScoreForm.title || !newScoreForm.score) {
    ElMessage.warning("科目名称和成绩不能为空");
    return;
  }

  // 初始化本科成绩数组（如果不存在）
  if (!props.studentForm.undergraduateTranscript) {
    props.studentForm.undergraduateTranscript = [];
  }

  // 生成新ID (取当前最大ID + 1)
  const maxId = Math.max(
    ...(props.studentForm.undergraduateTranscript.map((item) => item.id) || []),
    0
  );
  const newId = maxId + 1;

  // 添加到成绩列表
  props.studentForm.undergraduateTranscript.push({
    id: newId,
    title: newScoreForm.title,
    score: newScoreForm.score,
  });

  // 关闭对话框
  dialogVisible.value = false;
  ElMessage.success("添加成功");

  // 清空表单
  newScoreForm.title = "";
  newScoreForm.score = "";
};

// 获取性别文本
const getSexText = (sex) => {
  const sexValue = String(sex); // 确保转换为字符串
  if (sexValue === "1") return "男";
  if (sexValue === "2") return "女";
  if (sexValue === "3") return "其他";
  return "";
};

// 搜索本科院校
const remoteSearchCollege = (query) => {
  // 使用父组件的事件发射器
  emit("search-school", query);
};

// 这个函数已被 delMathScore 替代，保留此函数是为了兼容性
const delScoreInfo = (id) => {
  delMathScore(id);
};

// 搜索本科专业
const remoteSearchMajor = (query) => {
  if (!props.studentForm.undergraduateSchool) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 使用父组件的事件发射器
  emit("search-major", query);
};

// 搜索目标专业
const remoteSearchTargetMajor = (query) => {
  // 使用父组件的事件发射器
  emit("search-target-major", query);
};

// 搜索梦校
const remoteSearchDreamSchool = (query) => {
  if (!query) {
    dreamSchoolOptions.value = [];
    return;
  }

  dreamSchoolSearchLoading.value = true;
  console.log("搜索梦校:", query);

  // 检查缓存中是否已有该查询结果
  if (dreamSchoolSearchCache[query]) {
    dreamSchoolOptions.value = dreamSchoolSearchCache[query];
    dreamSchoolSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(dreamSchoolSearchTimeout.value);
  dreamSchoolSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("搜索梦校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          dreamSchoolOptions.value = options;
          // 添加到缓存
          dreamSchoolSearchCache[query] = options;

          // 如果选择了梦校，保存梦校名称
          if (props.studentForm.targetSchool) {
            const selectedSchool = options.find(
              (item) => item.value === props.studentForm.targetSchool
            );
            if (selectedSchool) {
              props.studentForm.targetSchoolName = selectedSchool.label;
            }
          }
        } else {
          dreamSchoolOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取梦校列表失败", err);
        dreamSchoolOptions.value = [];
      })
      .finally(() => {
        dreamSchoolSearchLoading.value = false;
      });
  }, 300);
};

// 监听本科院校变更
const handleCollegeChange = (value) => {
  // 根据选择的学校找到对应的名称
  const selectedSchool = props.schoolOptions.find(
    (item) => item.value === value
  );
  if (selectedSchool) {
    // 保存学校名称
    props.studentForm.undergraduateSchoolName = selectedSchool.label;
  }

  // 清空专业选择
  props.studentForm.undergraduateMajor = "";
  props.studentForm.undergraduateMajorName = "";

  // 发射事件通知父组件
  emit("school-change");
};

// 监听本科专业变更
const handleMajorChange = (value) => {
  // 根据选择的专业找到对应的名称
  const selectedMajor = props.majorOptions.find((item) => item.value === value);
  if (selectedMajor) {
    // 保存专业名称
    props.studentForm.undergraduateMajorName = selectedMajor.label;
  } else {
    // 清空专业名称
    props.studentForm.undergraduateMajorName = "";
  }
};

// 监听目标专业变更
const handleTargetMajorChange = (value) => {
  // 根据选择的专业找到对应的专业代码
  const selectedMajor = props.targetMajorOptions.find(
    (item) => item.value === value
  );
  if (selectedMajor) {
    console.log("选中的目标专业:", selectedMajor);
    // 保存专业名称
    props.studentForm.targetMajorName = selectedMajor.label;

    // 确保targetMajor正确设置为专业ID
    if (selectedMajor.id) {
      props.studentForm.targetMajor = selectedMajor.id;
    }

    // 设置专业代码（如果有）
    if (selectedMajor.code) {
      props.studentForm.majorCode = selectedMajor.code;
    } else {
      // 清空专业代码
      props.studentForm.majorCode = "";
    }

    console.log("设置后的targetMajor:", props.studentForm.targetMajor);
  } else {
    // 清空专业代码
    props.studentForm.majorCode = "";
    // 清空专业名称
    props.studentForm.targetMajorName = "";
  }
};

// 监听梦校变更
const handleDreamSchoolChange = (value) => {
  // 根据选择的梦校找到对应的名称
  const selectedSchool = dreamSchoolOptions.value.find(
    (item) => item.value === value
  );
  if (selectedSchool) {
    // 保存梦校名称
    props.studentForm.targetSchoolName = selectedSchool.label;
  }
};

// 处理标签变更
const handleTagsChange = (values) => {
  console.log("标签选择变更:", values);
  // 确保标签值是数组
  if (!Array.isArray(values)) {
    props.studentForm.tags = [];
    return;
  }

  // 确保所有值都是整数
  props.studentForm.tags = values
    .map((val) => {
      // 如果是对象，获取id属性
      const tagId = typeof val === "object" ? val.id : val;
      // 转换为整数
      return parseInt(tagId, 10);
    })
    .filter((id) => !isNaN(id) && id > 0); // 过滤掉无效的ID

  console.log("处理后的标签ID:", props.studentForm.tags);
};

// 提交数据
const handleSubmit = () => {
  ElMessage.success("报告提交成功！");
};

// 旋转动画相关
const isAnimating = ref(false);
const isHovering = ref(false);
const showAIOverlay = ref(false);

// 处理鼠标悬停
const handleMouseEnter = () => {
  isHovering.value = true;
};

// 处理鼠标离开
const handleMouseLeave = () => {
  isHovering.value = false;
};

// 获取考研年份列表
const fetchExamYears = async () => {
  examYearLoading.value = true;
  try {
    const res = await getExamYears();
    if (res.code === 0) {
      examYearOptions.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取考研年份列表失败");
    }
  } catch (error) {
    console.error("获取考研年份列表失败", error);
    ElMessage.error("获取考研年份列表失败，请稍后重试");
  } finally {
    examYearLoading.value = false;
  }
};

// 组件挂载时获取考研年份列表和标签数据
onMounted(() => {
  fetchExamYears();
  loadAllTags(); // 加载标签数据

  // 打印当前目标专业相关数据，用于调试
  console.log("目标专业ID:", props.studentForm.targetMajor);
  console.log("目标专业名称:", props.studentForm.targetMajorName);
  console.log("可选目标专业列表:", props.targetMajorOptions);

  // 如果有目标专业ID但没有对应名称，尝试设置默认显示名称
  if (props.studentForm.targetMajor && !props.studentForm.targetMajorName) {
    const targetMajor = props.targetMajorOptions.find(
      (item) => item.value === props.studentForm.targetMajor
    );
    if (targetMajor) {
      props.studentForm.targetMajorName = targetMajor.label;
    } else if (
      typeof props.studentForm.targetMajor === "string" ||
      typeof props.studentForm.targetMajor === "number"
    ) {
      // 如果只有ID没有名称，临时设置ID为名称
      props.studentForm.targetMajorName = `专业ID: ${props.studentForm.targetMajor}`;
    }
  }
});

// 切换遮罩层显示状态
const toggleAIOverlay = () => {
  showAIOverlay.value = !showAIOverlay.value;

  if (showAIOverlay.value) {
    // 显示遮罩层时触发动画
    isAnimating.value = true;
  } else {
    // 关闭遮罩层时重置主界面的动画状态
    setTimeout(() => {
      isAnimating.value = false;
    }, 500);
  }
};

// 处理窗口大小调整
const handleResize = () => {
  if (chartA) {
    chartA.resize();
  }
  if (chartB) {
    chartB.resize();
  }
};

onMounted(() => {
  nextTick(() => {
    window.addEventListener("resize", handleResize);
  });
});

// 清理资源
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

const echartsARef = ref(null);
const echartsBRef = ref(null);
let chartA = null;
let chartB = null;

// 监听性别值变化，确保在编辑模式下正确显示
watch(
  () => props.studentForm.sex,
  (newVal) => {
    console.log("性别值变化:", newVal);
    // 不再在这里修改 props.studentForm.sex，避免递归更新
  },
  { immediate: true }
);

// 监听目标专业ID变化，确保名称也随之更新
watch(
  () => props.studentForm.targetMajor,
  (newVal, oldVal) => {
    console.log("目标专业ID变化", oldVal, "->", newVal);

    // 如果newVal是0或"0"，则不做任何处理
    if (newVal === 0 || newVal === "0") {
      return;
    }

    // 如果ID有变化且存在，但名称不存在或不匹配，尝试更新名称
    if (
      newVal &&
      (!props.studentForm.targetMajorName ||
        props.studentForm.targetMajorName === `专业ID: ${oldVal}`)
    ) {
      // 1. 首先尝试从现有选项中找
      const selectedMajor = props.targetMajorOptions.find(
        (item) => item.value === newVal
      );
      if (selectedMajor) {
        props.studentForm.targetMajorName = selectedMajor.label;
        console.log("从选项列表中找到并更新目标专业名称:", selectedMajor.label);
      }
      // 2. 如果找不到，且值不是0，则临时设置ID为名称
      else if (
        (typeof newVal === "string" || typeof newVal === "number") &&
        newVal !== 0 &&
        newVal !== "0"
      ) {
        props.studentForm.targetMajorName = `专业ID: ${newVal}`;
        console.log("未找到目标专业名称，临时设置为ID");

        // 3. 如果远程搜索未完成，尝试进行搜索
        if (props.targetMajorOptions.length === 0) {
          console.log("尝试搜索目标专业");
          remoteSearchTargetMajor("");
        }
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.generate-container {
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
  min-height: 100vh;
}

.report-container {
  padding: 20px;
  background-color: #fff;
  margin: 20px;
  border-radius: 4px;
}

/* 步骤样式 */
.steps {
  margin-top: 22px;
}

.step-section {
  margin-bottom: 30px;
}

.step-header {
  margin-bottom: 15px;
}

.step-title {
  width: 320px;
  height: 40px;
  padding: 0 15px;
  padding-top: 14px;
  color: #fff;
  border-radius: 5px;
  font-weight: bold;
  background-image: url("@/assets/images/step-bg.png");
  background-repeat: no-repeat;
  text-align: center;
}

.step-content {
  padding: 20px;
  border-radius: 5px;
  position: relative;
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
}

.step-num-tag {
  background-image: url("@/assets/images/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.wide-item {
  grid-column: span 2;
}

.item-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

.full-width {
  width: 100%;
}

.form-grid,
.english-grid,
.school-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper) {
    border-color: #1bb394 !important;

    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper.is-focus) {
    border-color: #1bb394 !important;
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  /* 下拉菜单样式 */
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }

  /* 多选框样式 */
  :deep(.el-select .el-tag) {
    background-color: #e6f7f1;
    border-color: #1bb394;
    color: #1bb394;
  }

  /* select下拉箭头颜色 */
  :deep(.el-select .el-input__suffix) {
    color: #1bb394;
  }
}

.score-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #fff inset;
  }
}

/* 成绩表格 */
.score-grid,
.english-grid {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.score-row,
.english-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.score-item,
.english-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.sel-no-border {
  :deep(.el-select__wrapper) {
    border-color: #fff !important;
    box-shadow: none;
  }
  :deep(.el-select__selected-item) {
    text-align: center;
  }
}
.score-item {
  border: #1bb394 1px solid;
  border-radius: 10px;
  padding-left: 10px;
  position: relative;
  cursor: move;
  /* 显示可拖动光标 */
  transition: all 0.2s ease;

  &.drag-over {
    border: 2px dashed #1bb394;
    background-color: rgba(27, 179, 148, 0.05);
    transform: scale(1.01);
  }

  .score-label {
    height: 100%;
    width: 180px;
    border-right: #666 1px solid;
    box-shadow: 2px 0 2px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-close {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.score-label,
.english-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
}

.english-label {
  text-align: center;
}

.score-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  width: 100%;
}

.score-divider {
  margin: 0 5px;
  color: #999;
}

/* 学校选择 */
.school-grid {
  margin-left: 20px;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 表格样式 */
.score-table {
  width: 100%;
  border: 1px solid #1bb394;
  margin-top: 20px;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #1bb394;
}

.th-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 14px;
  border-right: 1px solid #1bb394;

  &:last-child {
    border-right: none;
  }

  .sel-no-border {
    :deep(.el-select__wrapper) {
      border-color: #fff !important;

      box-shadow: none;
    }
  }
}

.table-row-score {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
}

.td-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #1bb394;

  &:last-child {
    border-right: none;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-input__inner) {
    text-align: center;
    box-shadow: none;
  }
}

.td-cell .el-input {
  width: 100%;
}

.table-input {
  width: 100%;
}

/* 个性化需求和薄弱模块 */
.personal-demands,
.expertise-advice {
  margin-left: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demands-label,
.advice-label {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  width: 100px;
  height: 30px;
  line-height: 30px;
}

.demands-input,
.advice-input {
  flex: 1;
}

/* 表单样式覆盖 */
:deep(.el-textarea__inner) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

/* 按钮样式 */
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.action-button {
  width: 104px;
  height: 38px;
  background: #1bb394;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1bb394;
}

/* 英语栏目样式 */
.english-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.english-label {
  min-width: 100px;
  margin-right: 15px;
}

.no-border-select .el-input {
  border: none;
  box-shadow: none;
}

.no-border-select {
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }
}

/* 标签样式 */
.tag {
  padding: 2px 8px;
  font-size: 12px;
}

.tag-double {
  background-color: #1bb394;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

.tag-green {
  background-color: #1bb394;
  color: white;
}

.tag-orange {
  background-color: #ff9900;
  color: white;
}

.tag-purple {
  background-color: #8e6df8;
  color: white;
}

.tag-blue {
  background-color: #409eff;
  color: white;
}

.tag-default {
  background-color: #909399;
  color: white;
}

.tag-display {
  margin-top: 5px;
}

/* 选中名称显示样式 */
.selected-name {
  margin-top: 5px;
  font-size: 13px;
  color: #1bb394;
  font-weight: bold;
}
</style>
<style>
.el-message-box__btns .el-button--primary {
  background-color: #1bb394 !important;
  /* 绿色背景 */
  border: none !important;
}

.el-select-dropdown__item.is-selected {
  color: #1bb394 !important;
  font-weight: bold;
}

/* 英语能力下拉框样式 */
.english-ability-dropdown {
  text-align: center;
}

.english-ability-dropdown .el-select-dropdown__item {
  text-align: center;
}

/* 确保英语能力下拉框中的文字居中 */
.english-item .el-select .el-input__inner {
  text-align: center;
}

/* 确保英语能力下拉框的宽度与其他输入框一致 */
.english-item .el-select {
  width: 100%;
}

/* 确保下拉框中的选项文字居中 */
:deep(.el-select-dropdown__item) {
  text-align: center;
}
</style>
