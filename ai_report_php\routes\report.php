<?php
use <PERSON><PERSON>\Route;
Route::group("/api", function () {
    Route::get('/report', [app\controller\ReportController::class, 'reportList']);
    Route::get('/get_years', [app\controller\ReportController::class, 'getExamYears']);
    Route::get('/check_spider_login', [app\controller\ReportController::class, 'checkSpiderLogin']);
    Route::get('/coskey', [app\controller\ConfigController::class, 'getCosKey']);
    Route::post('/uploadfile', [app\controller\ConfigController::class, 'uploadFile']);
    Route::get('/report_info', [app\controller\ReportController::class, 'getReportInfo']);
    Route::post('/save_report', [app\controller\ReportController::class, 'saveReport']);
    Route::put('/report/pdf-url', [app\controller\ReportController::class, 'updatePdfUrl']);

    Route::get('/report_count', [app\controller\ReportController::class, 'getReportCount']);


    Route::get('/report_detail', [app\controller\ReportController::class, 'getReportDetail']);

// ... 其他路由 ...
})->middleware([app\middleware\JwtMiddleware::class]);
