import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import * as path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve("./src"),
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8787',
        changeOrigin: true,
        ws: true, // 支持 WebSocket 和 SSE
        timeout: 0, // 禁用超时，适合长连接
        configure: (proxy, options) => {
          // 专门为 SSE 请求配置
          proxy.on('proxyReq', (proxyReq, req, res) => {
            if (req.url && req.url.includes('stream')) {
              console.log('SSE 请求代理:', req.url);
              // 设置 SSE 相关头部
              proxyReq.setHeader('Accept', 'text/event-stream');
              proxyReq.setHeader('Cache-Control', 'no-cache');
              proxyReq.setTimeout(0); // 禁用超时
            }
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            if (req.url && req.url.includes('stream')) {
              console.log('SSE 响应代理:', proxyRes.statusCode);
              // 确保 SSE 响应不被缓存
              proxyRes.headers['cache-control'] = 'no-cache';
              proxyRes.headers['connection'] = 'keep-alive';
            }
          });
        }
      }
    }
  },
});
